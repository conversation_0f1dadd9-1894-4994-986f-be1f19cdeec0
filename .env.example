# Environment Variables for ICB Admin Panel
# Copy this file to .env and update the values as needed

# Backend API URL
# This is the main API endpoint for the admin panel
VITE_BACKEND_URL=https://admin-api.indiancashback.com/api

# Node Environment
# Options: development, production, test
NODE_ENV=production

# Optional: Custom port for development server
# PORT=3000

# Optional: Enable/disable source maps in production
# GENERATE_SOURCEMAP=false

# Optional: Custom build directory
# BUILD_PATH=build

# Optional: Public URL for the application
# PUBLIC_URL=/

# Optional: Disable chunk splitting for smaller builds
# INLINE_RUNTIME_CHUNK=false

# Optional: Custom analyzer settings
# ANALYZE=false

# Optional: Custom CI settings
# CI=false

# Optional: Disable ESLint during build
# DISABLE_ESLINT_PLUGIN=false

# Optional: Custom image optimization settings
# IMAGE_INLINE_SIZE_LIMIT=10000

# Optional: Custom TypeScript settings
# TSC_COMPILE_ON_ERROR=false

# Optional: Custom webpack settings
# FAST_REFRESH=true
