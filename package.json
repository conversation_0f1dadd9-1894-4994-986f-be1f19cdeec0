{"name": "@coreui/coreui-free-react-admin-template", "version": "4.4.0", "description": "CoreUI Free React Admin Template", "proxy": "https://admin-api.indiancashback.com", "bugs": {"url": "https://github.com/coreui/coreui-free-react-admin-template/issues"}, "repository": {"type": "git", "url": "**************:coreui/coreui-free-react-admin-template.git"}, "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people)", "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview --port 3000"}, "dependencies": {"@ckeditor/ckeditor5-react": "^5.0.6", "@cloudinary/react": "^1.11.0", "@cloudinary/url-gen": "^1.9.1", "@coreui/chartjs": "^3.0.0", "@coreui/coreui": "^4.2.1", "@coreui/coreui-free-react-admin-template": "file:", "@coreui/icons": "^2.1.0", "@coreui/icons-react": "^2.1.0", "@coreui/react": "^4.3.1", "@coreui/react-chartjs": "^2.1.0", "@coreui/react-pro": "^4.8.0", "@coreui/utils": "^1.3.1", "@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^2.0.1", "@vitejs/plugin-react": "^4.4.0", "axios": "^1.3.0", "chart.js": "^3.9.1", "ckeditor5-custom-build": "file:ckeditor5", "classnames": "^2.3.1", "core-js": "^3.24.1", "destr": "^2.0.5", "million": "^3.0.3", "moment-timezone": "^0.5.46", "prop-types": "^15.8.1", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.0", "react-hot-toast": "^2.4.0", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-select": "^5.8.0", "redux": "4.2.0", "simplebar-react": "^2.4.1", "vite": "^6.3.0", "vite-plugin-svgr": "^4.3.0", "yup": "^1.3.2"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^14.4.3", "@vitejs/plugin-legacy": "^6.1.0", "auto-changelog": "~2.4.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "2.7.1", "sass": "^1.54.4", "web-vitals": "^2.1.4"}, "engines": {"node": ">=10", "npm": ">=6"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}