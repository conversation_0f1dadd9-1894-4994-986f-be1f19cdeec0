import React from "react";
import CIcon from "@coreui/icons-react";
import {
  cilBadge,
  cilBarcode,
  cilBraille,
  cilCalculator,
  cilChartPie,
  cilCursor,
  cilDescription,
  cilGift,
  cilGroup,
  cilInfo,
  cilLayers,
  cilLibrary,
  cilListRich,
  cilPuzzle,
  cilSpeedometer,
  cilWc,
} from "@coreui/icons";
import { CNavGroup, CNavItem } from "@coreui/react";
import { access } from "./utils/access.jsx";

const _nav = [
  {
    component: CNavItem,
    name: "Dashboard",
    to: "/dashboard",
    icon: <CIcon icon={cilSpeedometer} customClassName="nav-icon" />,
    badge: {
      color: "info",
      text: "NEW",
    },
  },
  {
    component: CNavGroup,
    name: "Admins",
    to: "/admins",
    icon: <CIcon icon={cilCalculator} customClassName="nav-icon" />,
    access: access.admins,
    items: [
      {
        component: CNavItem,
        name: "All Admins",
        to: "/admins/all-admins",
      },
      {
        component: CNavItem,
        name: "Create Admin",
        to: "/admins/create-admin",
      },
      {
        component: CNavItem,
        name: "Admin Logs",
        to: "/admins/logs",
        icon: <CIcon icon={cilListRich} customClassName="nav-icon" />,
      },
    ],
  },

  {
    component: CNavGroup,
    name: "Access controls",
    to: "/access-controls",
    icon: <CIcon icon={cilCursor} customClassName="nav-icon" />,
    access: access.accessControls,
    items: [
      {
        component: CNavItem,
        name: "All Access controls",
        to: "/access-controls/all-access-controls",
      },
      {
        component: CNavItem,
        name: "Create Access controls",
        to: "/access-controls/create-access-controls",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Banner Images",
    to: "/banners",
    icon: <CIcon icon={cilDescription} customClassName="nav-icon" />,
    access: access.banners,
    items: [
      {
        component: CNavItem,
        name: "Desktop Banners",
        to: "/banners/desktop/all-stories",
      },
      {
        component: CNavItem,
        name: "Mobile Stories",
        to: "/banners/stories/all-stories",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Quick Access",
    to: "/quick-access",
    icon: <CIcon icon={cilDescription} customClassName="nav-icon" />,
    access: access.quickAccess,
    items: [
      {
        component: CNavItem,
        name: "All Quick Access",
        to: "/quick-access/all-quick-access",
      },
      {
        component: CNavItem,
        name: "Create Quick Access",
        to: "/quick-access/create",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Affiliation",
    to: "/affiliation",
    icon: <CIcon icon={cilGroup} customClassName="nav-icon" />,
    access: access.affiliations,
    items: [
      {
        component: CNavItem,
        name: "All Affiliations",
        to: "/affiliation/all-affiliations",
      },
      {
        component: CNavItem,
        name: "Create Affiliation",
        to: "/affiliation/create-affiliation",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Categories",
    to: "/categories",
    icon: <CIcon icon={cilBarcode} customClassName="nav-icon" />,
    access: access.categories,
    items: [
      {
        component: CNavItem,
        name: "All Categories",
        to: "/category/all-categories",
      },
      {
        component: CNavItem,
        name: "Create Category",
        to: "/category/create-category",
      },
      {
        component: CNavItem,
        name: "All Sub Categories",
        to: `/category/all-sub-category/${null}`,
      },
      {
        component: CNavItem,
        name: "Create Sub Category",
        to: "/category/create-sub-category",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Stores",
    to: "/store",
    icon: <CIcon icon={cilDescription} customClassName="nav-icon" />,
    access: access.stores,
    items: [
      {
        component: CNavItem,
        name: "All Stores",
        to: `/store/all-stores/${null}`,
      },
      {
        component: CNavItem,
        name: "Create Store",
        to: "/store/create-store",
      },
    ],
  },
  {
    component: CNavItem,
    icon: <CIcon icon={cilChartPie} customClassName="nav-icon" />,
    name: "Trending Stores ",
    to: "/store/trending-stores",
  },
  {
    component: CNavGroup,
    name: "Store Reviews",
    to: "/store-reviews",
    icon: <CIcon icon={cilDescription} customClassName="nav-icon" />,
    access: access.storeReviews,
    items: [
      {
        component: CNavItem,
        name: "All Store Reviews",
        to: "/store-reviews/all-store-reviews",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Store Categories",
    to: "/store-categories",
    icon: <CIcon icon={cilDescription} customClassName="nav-icon" />,
    access: access.storeCategories,
    items: [
      {
        component: CNavItem,
        name: "All Store Categories",
        to: "/store-categories/all-store-categories",
      },
      {
        component: CNavItem,
        name: "Create Store Category",
        to: "/store-categories/create-store-category",
      },
      {
        component: CNavItem,
        name: "Store Category Histories",
        to: "/store-categories/category-histories",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Offers",
    to: "/offers",
    icon: <CIcon icon={cilBadge} customClassName="nav-icon" />,
    access: access.offers,
    items: [
      {
        component: CNavItem,
        name: "All Offers",
        to: "/offers/all-offers",
      },
      {
        component: CNavItem,
        name: "Create Offer",
        to: "/offers/create-offer",
      },
    ],
  },
  {
    component: CNavItem,
    name: "Trending Offers",
    to: "/offers/trending-offers",
    icon: <CIcon icon={cilBraille} customClassName="nav-icon" />,
    access: access.trendingOffers,
  },
  {
    component: CNavGroup,
    name: "Ongoing Sales",
    to: "/offers/ongoing-sales",
    icon: <CIcon icon={cilBadge} customClassName="nav-icon" />,
    access: access.ongoingSales,
    items: [
      {
        component: CNavItem,
        name: "All Ongoing Sales",
        to: "/offers/ongoing-sales/all-sales",
      },
      {
        component: CNavItem,
        name: "Create Ongoing Sale",
        to: "/offers/ongoing-sales/create",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "GiftCards",
    to: "/giftcards",
    icon: <CIcon icon={cilGift} customClassName="nav-icon" />,
    access: access.giftCard,
    items: [
      {
        component: CNavItem,
        name: "All Giftcards",
        to: "/giftcards/all-giftcards",
      },
      {
        component: CNavItem,
        name: "Create Giftcard",
        to: "/giftcards/create-giftcard",
      },
      {
        component: CNavItem,
        name: " Giftcard Orders",
        to: "/giftcards/orders/all-orders",
        icon: <CIcon icon={cilPuzzle} customClassName="nav-icon" />,
      },
      {
        component: CNavItem,
        name: "Icb Giftcard Orders",
        to: "/icb-giftcards/all-orders",
        icon: <CIcon icon={cilPuzzle} customClassName="nav-icon" />,
      },
      {
        component: CNavGroup,
        name: "Gift Card Offers",
        to: "/giftcards",
        icon: <CIcon icon={cilLayers} customClassName="nav-icon" />,
        items: [
          {
            component: CNavItem,
            name: "All Gift Card Offers",
            to: "/giftcards/offers/all-offers",
          },
          {
            component: CNavItem,
            name: "Create Gift Card Offer",
            to: "/giftcards/offers/create-offers",
          },
        ],
      },
      {
        component: CNavGroup,
        name: "Gift Card Sliders",
        to: "/giftcards",
        icon: <CIcon icon={cilLibrary} customClassName="nav-icon" />,
        items: [
          {
            component: CNavItem,
            name: "All Gift Card Sliders",
            to: "/giftcards/sliders/all-sliders",
          },
          {
            component: CNavItem,
            name: "Create Gift Card Slider",
            to: "/giftcards/sliders/create-sliders",
          },
        ],
      },
    ],
  },
  {
    component: CNavItem,
    name: "User Clicks",
    to: "/clicks/all-clicks",
    icon: <CIcon icon={cilBraille} customClassName="nav-icon" />,
    access: access.clicks,
  },
  {
    component: CNavItem,
    name: "Auto Tracked",
    to: "/auto-tracked/all-auto-tracked",
    icon: <CIcon icon={cilBraille} customClassName="nav-icon" />,
    access: access.earnings,
  },
  {
    component: CNavGroup,
    name: "Approved Earnings",
    to: "/earnings",
    icon: <CIcon icon={cilBraille} customClassName="nav-icon" />,
    access: access.earnings,
    items: [
      {
        component: CNavItem,
        name: "Earnings",
        to: "/earnings",
      },
      {
        component: CNavItem,
        name: "Tracked For Confirm",
        to: "/earnings/tracked-for-confirm",
      },
      {
        component: CNavItem,
        name: "Tracked For Cancel",
        to: "/earnings/tracked-for-cancel",
      },
    ],
  },
  {
    component: CNavItem,
    name: "Payments Requests",
    to: "/payments/requests/all-requests",
    icon: <CIcon icon={cilInfo} customClassName="nav-icon" />,
  },

  {
    component: CNavItem,
    name: "Missing Cashbacks",
    to: "/payments/missing/all-cashbacks",
    icon: <CIcon icon={cilInfo} customClassName="nav-icon" />,
    access: access.missingCashback,
  },

  {
    component: CNavGroup,
    name: "Users",
    to: "/users",
    icon: <CIcon icon={cilWc} customClassName="nav-icon" />,
    access: access.users,
    items: [
      {
        component: CNavItem,
        name: "All Users",
        to: "/users/all-users",
      },
      {
        component: CNavItem,
        name: "Personal Interests",
        to: "/users/all-personal-interests",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Testimonials",
    to: "/testimonials",
    icon: <CIcon icon={cilWc} customClassName="nav-icon" />,
    access: access.testimonials,
    items: [
      {
        component: CNavItem,
        name: "All Testimonials",
        to: "/testimonials/all-testimonials",
      },
      {
        component: CNavItem,
        name: "Create Testimonial",
        to: "/testimonials/create",
      },
    ],
  },
  {
    component: CNavGroup,
    name: "Terms And Privacy",
    to: "/terms-and-privacy",
    icon: <CIcon icon={cilWc} customClassName="nav-icon" />,
    access: access.termsAndPrivacy,
    items: [
      {
        component: CNavItem,
        name: "All Terms And Privacies",
        to: "/terms-and-privacy/all-terms-privacies",
      },
      {
        component: CNavItem,
        name: "Create Terms And Privacy",
        to: "/terms-and-privacy/create",
      },
      {
        component: CNavItem,
        name: "Terms And Privacy Logs",
        to: "/terms-and-privacy/logs",
      },
    ],
  },
];

export default _nav;
