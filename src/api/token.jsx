import { destr } from "destr";
class TokenService {
  getLocalRefreshToken() {
    const refreshToken = destr(localStorage.getItem("refreshToken"));
    return refreshToken;
  }

  getLocalAccessToken() {
    const accessToken = destr(localStorage.getItem("accessToken"));
    return accessToken;
  }

  updateLocalRefreshToken(token) {
    localStorage.setItem("refreshToken", JSON.stringify(token));
  }
  updateLocalAccessToken(token) {
    localStorage.setItem("accessToken", JSON.stringify(token));
  }
  getAdmin() {
    return destr(localStorage.getItem("admin"));
  }
  removeAdmin() {
    localStorage.removeItem("admin");
  }
}

export default new TokenService();
