import React, { Suspense, useEffect } from "react";
import { Navigate, Route, Routes, useNavigate } from "react-router-dom";
import { CContainer, CSpinner } from "@coreui/react";
import routes from "../routes";

const AppContent = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const adminInfo = JSON.parse(localStorage.getItem("admin"));
    if (!adminInfo) {
      navigate("/login");
    }
  },[navigate]);

  return (
    <CContainer fluid >
      <Suspense fallback={<CSpinner color="primary" />}>
        <Routes>
          {routes.map((route, idx) => {
            return (
              route.element && (
                <Route
                  key={idx}
                  path={route.path}
                  exact={route.exact}
                  name={route.name}
                  element={<route.element />}
                />
              )
            );
          })}
          <Route path="/" element={<Navigate to="dashboard" replace />} />
        </Routes>
      </Suspense>
    </CContainer>
  );
};

export default React.memo(AppContent);
