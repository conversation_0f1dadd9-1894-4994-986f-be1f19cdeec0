import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";

import { CSidebar, CSidebarNav } from "@coreui/react";

import { AppSidebarNav } from "./AppSidebarNav";

import SimpleBar from "simplebar-react";
import "simplebar/dist/simplebar.min.css";

// sidebar nav config
import navigation from "../_nav";
import { fetchAllMiddlewares, handleSideBar } from "src/redux/features";

const AppSidebar = () => {
  const dispatch = useDispatch();
  const sidebarShow = useSelector((state) => state.sideBar.sidebar);

  const [items, setItems] = useState([]);
  const { allMiddlewares } = useSelector((state) => state.middleware);

  useEffect(() => {
    dispatch(fetchAllMiddlewares({}));
  }, [dispatch]);

  useEffect(() => {
    try {
      const adminInfo = JSON.parse(localStorage.getItem("admin"));
      if (allMiddlewares && adminInfo) {
        if (adminInfo?.role === "super admin") {
          setItems(navigation);
        } else {
          const someArray = [];
          allMiddlewares.filter(
            (item) =>
              item.name === adminInfo?.role && someArray.push(...item.features)
          );
          const finalData = navigation.filter((nav) =>
            nav?.access?.some((element) => someArray.includes(element))
          );
          setItems(finalData);
        }
      }
    } catch (error) {
      console.log(error);
    }
  }, [allMiddlewares]);

  return (
    <CSidebar
      position="fixed"
      visible={sidebarShow}
      onVisibleChange={(visible) => {
        dispatch(handleSideBar(visible));
      }}
    >
      <CSidebarNav>
        <SimpleBar>
          <AppSidebarNav items={items} />
        </SimpleBar>
      </CSidebarNav>
    </CSidebar>
  );
};

export default React.memo(AppSidebar);
