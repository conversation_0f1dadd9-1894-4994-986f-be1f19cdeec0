import React from "react";
import { CRow, CCol } from "@coreui/react";

function TextAreaField({ state, setState, title, type, error, rows = 5 }) {
  return (
    <CRow className="my-4">
      <CCol md={3} lg={3} xl={3}>
        <p className="me-3 my-auto text-capitalize  fs-6">{title}</p>
      </CCol>
      <CCol md={9} lg={9} xl={9} className="position-relative ">
        <textarea
          className={`w-100 border ${error && "border-danger"} rounded p-2 `}
          placeholder={title}
          type={type}
          {...setState(state)}
          rows={rows}
        />

        <p style={{ right: 0 }} className=" text-danger  position-absolute">
          {error}
        </p>
      </CCol>
    </CRow>
  );
}

export default TextAreaField;
