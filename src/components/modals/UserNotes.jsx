import { CButton, CModal, CModalBody } from '@coreui/react';
import React, { useEffect, useState } from 'react'
import { writeUserNotes } from 'src/redux/features';

const UserNotes = ({
    dispatch,
    userDetails,
    showUserNoteModel,
    setShowUserNoteModal,
  }) => {
    const [notes, setNotes] = useState("");
    useEffect(() => {
      if(userDetails){
        setNotes(userDetails?.notes)
      }
    }, [userDetails])
    
    const handleUpdateNotes = () => {
      dispatch(writeUserNotes({ userId: userDetails._id, notes }));
    };
    return (
      <CModal
        visible={showUserNoteModel}
        onClose={() => setShowUserNoteModal(false)}
      >
        <CModalBody>
          <div className="d-flex justify-content-around my-3">
            <p>
              User Name:{" "}
              <span className="fw-bold text-capitalize">{userDetails?.name}</span>
            </p>
            <p>
              User Unique Id: <span className="fw-bold">{userDetails?.uid}</span>
            </p>
          </div>
          <div className=" ">
            <label htmlFor="">User Notes:</label>{" "}
            <textarea
              name=""
              id=""
              placeholder="write notes..."
              className="w-100 border rounded  p-2"
              rows="5"
              required
              onChange={(e) => setNotes(e.target.value)}
              value={notes}
            ></textarea>
          </div>
        </CModalBody>
        <div className="d-flex justify-content-between align-items-center">
          <CButton
            color="danger"
            size="sm"
            className="w-25 mx-auto my-3 text-white"
            onClick={() => setShowUserNoteModal(false)}
          >
            Close
          </CButton>
          <CButton
            color="success"
            size="sm"
            className="w-25 mx-auto my-3 text-white"
            onClick={() => handleUpdateNotes()}
          >
            Add Note
          </CButton>
        </div>
      </CModal>
    );
  };
export default UserNotes