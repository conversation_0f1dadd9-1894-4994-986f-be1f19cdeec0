import React, { useEffect, useState } from 'react'
import Select from 'react-select';
import makeAnimated from 'react-select/animated';
const animatedComponents = makeAnimated();

function SearchAndMultiSelect({ style, array, handleSelectedValue, placeholder, defaultValue }) {
    const [options, setOptions] = useState([])
    const [selectedOption, setSelectedOption] = useState(null)

    useEffect(() => {
        if (array && array.length !== 0) {
            setOptions(array.map((item) => {
                return {
                    label: item.name || item.title,
                    value: item._id
                }
            }))
        }
        if (defaultValue) {
            setSelectedOption(defaultValue)
        }

    }, [array, defaultValue])

    const handleSelectOption = (value) => {
        setSelectedOption(value)
        handleSelectedValue(value)
    }
    return (
        <Select
            search
            isMulti
            components={animatedComponents}
            value={selectedOption}
            onChange={handleSelectOption}
            className={style ? style : "w-100"}
            placeholder={placeholder ? placeholder : "Please Select One..."}
            options={options} />
    )
}

export default SearchAndMultiSelect