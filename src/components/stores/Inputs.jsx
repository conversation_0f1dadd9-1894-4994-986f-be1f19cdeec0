import { CRow, CCol, CFormInput } from "@coreui/react"

const Inputs = ({ state, setState, title, type, disabled,required }) => {
    return (
        <CRow className="my-4">
            <CCol md={3} lg={3} xl={3}>
                <p className='me-3 my-auto text-capitalize'>{title} <span className="text-danger">{required&& "*"}</span></p>
            </CCol>
            <CCol md={9} lg={9} xl={9}>
                <input className="border rounded w-100 p-2" placeholder={title} type={type} value={state}
                    onChange={((e) => setState(e.target.value))} disabled={disabled} required={required} />
            </CCol>
        </CRow>
    )
}

export default Inputs;