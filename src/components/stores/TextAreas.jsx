import { CCol, CFormTextarea, CRow }  from "@coreui/react"

const TextAreas = ({ title, state, setState,required }) => {
    return (
        <CRow className="my-3">
            <CCol md={3} lg={3} xl={3}>
                <p className='me-3 my-auto'>{title}</p>
            </CCol>
            <CCol md={9} lg={9} xl={9}>
                <textarea className="w-100 border rounded" placeholder={title} type="text" value={state}
                    onChange={((e) => setState(e.target.value))} required={required} />
            </CCol>
        </CRow>
    )
}

export default TextAreas;