export const removePTagsFromStartAndEnd = (inputString) => {
    // Regular expression to match <p> tags at the start and end of the string
    let regex = /^(<p>)(.*?)(<\/p>)/;

    // Replace <p> tags at the start with an empty string
    let result = inputString.replace(regex, '$2');

    // Check if the result still starts with <p>, if so, remove the <p> tag from the end
    if (result.startsWith('<p>')) {
        result = result.substring(3);
    }

    // Check if the result ends with </p>, if so, remove the </p> tag from the end
    if (result.endsWith('</p>')) {
        result = result.substring(0, result.length - 4);
    }

    return result;
}