import React from 'react'
import { AppContent, AppSidebar, AppHeader } from '../components/index.jsx'
import PreviewModal from 'src/components/fileManagers/PreviewModal.jsx'
import ReferralCommissionModal from 'src/components/modals/ReferralCommissionModal.jsx'

const DefaultLayout = () => {
  return (
    <div>
      <AppSidebar />
      <div className="wrapper d-flex flex-column min-vh-100 bg-light">
        <AppHeader />
        <div className="body flex-grow-1 px-3 ">
          <AppContent />
        </div>
        <div className="my-5">
        </div>
        <PreviewModal />
        <ReferralCommissionModal/>
      </div>
    </div>
  )
}

export default DefaultLayout
