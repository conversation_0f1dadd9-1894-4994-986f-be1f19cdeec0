import { toast } from "react-hot-toast";
import Warning from "src/components/alerts/Warning/Warning";
import { affiliateIds } from "src/constants/affiliateIds";

/**
 * Generates a deeplink based on the selected store's affiliation and offer link
 * @param {Object} params - Parameters for deeplink generation
 * @param {Object} params.selectedStore - The selected store object
 * @param {string} params.offerLink - The original offer link
 * @param {string} params.baseLink - The base affiliate link
 * @returns {string|null} - Generated deeplink or null if generation fails
 */
export const generateDeeplink = ({ selectedStore, offerLink, baseLink }) => {
  if (!selectedStore || !offerLink) {
    toast.custom(
      <Warning message={"Store and offer link are required for deeplink generation"} />
    );
    return null;
  }

  let updatedOfferLink;

  try {
    if (selectedStore.affiliation === affiliateIds.admitad) {
      const encodedOfferLink = encodeURIComponent(offerLink);
      updatedOfferLink = selectedStore.affiliateLink + "?ulp=" + encodedOfferLink;
    } else if (selectedStore.affiliation === affiliateIds.amazon) {
      if (offerLink.indexOf("?") !== -1) {
        updatedOfferLink = offerLink + "&tag=revo21-21";
      } else {
        updatedOfferLink = offerLink + "?tag=revo21-21";
      }
    } else if (selectedStore.affiliation === affiliateIds.souq) {
      const affiliateParams =
        "&phgid=1101lKgZ&pubref=||||&utm_source=affiliate_hub&utm_medium=cpt&utm_content=affiliate&utm_campaign=100l2&u_type=text&u_title=&u_c=&u_fmt=&u_a=1101l11808&u_as=||||";
      if (offerLink.indexOf("?") !== -1) {
        updatedOfferLink = offerLink + affiliateParams;
      } else {
        updatedOfferLink = offerLink + "?" + affiliateParams.substring(1); // Remove the leading "&" for the first parameter
      }
    } else if (selectedStore?.affiliation === affiliateIds.flipkart) {
      let sanitizedLink = offerLink.replace("https://www.flipkart.com/", "");
      if (offerLink.indexOf("?") !== -1) {
        updatedOfferLink =
          "https://dl.flipkart.com/dl/" + sanitizedLink + "&affid=connectin";
      } else {
        updatedOfferLink =
          "https://dl.flipkart.com/dl/" + sanitizedLink + "?affid=connectin";
      }
    } else if (selectedStore?.affiliation === affiliateIds.adsplay) {
      let baseLinkProcessed = baseLink.replace(
        "http://affiliates.adsplay.in/trackingcode.php?",
        ""
      );
      updatedOfferLink =
        "http://affiliates.adsplay.in/trackingcode_deep.php?" +
        baseLinkProcessed +
        "&dlurl=" +
        offerLink;
    } else if (selectedStore.affiliation === affiliateIds.shopclues) {
      let sanitizedOfferLink = encodeURIComponent(offerLink);
      let updatedBaseLink = baseLink.split("&ckmrdr=")[0];
      updatedOfferLink = updatedBaseLink + "&ckmrdr=" + sanitizedOfferLink;
    } else if (selectedStore.affiliation === affiliateIds.infibeam) {
      if (offerLink.indexOf("?") !== -1) {
        updatedOfferLink = offerLink + "&trackId=icash";
      } else {
        updatedOfferLink = offerLink + "?trackId=icash";
      }
    } else if (selectedStore.affiliation === affiliateIds.twogud) {
      if (offerLink.indexOf("?") !== -1) {
        updatedOfferLink = offerLink + "&affid=connectin";
      } else {
        updatedOfferLink = offerLink + "?affid=connectin";
      }
    } else if (selectedStore.affiliation === affiliateIds.omg) {
      let sanitizedBaseLink = baseLink.split("&r=")[0];
      updatedOfferLink = sanitizedBaseLink + "&r=" + offerLink;
    } else if (selectedStore.affiliation === affiliateIds.affle) {
      let encodedOfferLink = encodeURIComponent(offerLink);
      let updatedBaseLink = baseLink.split("&url=")[0];
      updatedOfferLink = updatedBaseLink + "&url=" + encodedOfferLink;
    } else if (selectedStore.affiliation === affiliateIds.flickstree) {
      let encodedOfferLink = encodeURIComponent(offerLink);
      let updatedBaseLink = baseLink.split("&url=")[0];
      updatedOfferLink = updatedBaseLink + "&url=" + encodedOfferLink;
    } else if (selectedStore.affiliation === affiliateIds.clickonik) {
      let encodedOfferLink = encodeURIComponent(offerLink);
      let updatedBaseLink = baseLink.split("&url=")[0];
      updatedOfferLink = updatedBaseLink + "&url=" + encodedOfferLink;
    } else if (selectedStore.affiliation === affiliateIds.impact) {
      let encodedOfferLink = encodeURIComponent(offerLink);
      let updatedBaseLink = baseLink.split("?u=")[0];
      updatedOfferLink = updatedBaseLink + "?u=" + encodedOfferLink;
    } else if (selectedStore.affiliation === affiliateIds.analytics) {
      let encodedOfferLink = encodeURIComponent(offerLink);
      if (baseLink.indexOf("?") !== -1) {
        updatedOfferLink = baseLink + "&url=" + encodedOfferLink;
      } else {
        updatedOfferLink = baseLink + "?url=" + encodedOfferLink;
      }
    } else {
      toast.custom(<Warning message={"Can't set deep link for this affiliate"} />);
      return null;
    }

    return updatedOfferLink;
  } catch (error) {
    toast.custom(<Warning message={"Error generating deeplink: " + error.message} />);
    return null;
  }
};

/**
 * Checks if deeplink generation is supported for the given store
 * @param {Object} storeDetails - Store details object
 * @returns {boolean} - Whether deeplink is supported
 */
export const isDeeplinkSupported = (storeDetails) => {
  return (
    storeDetails?.deepLinkEnable &&
    Object.values(affiliateIds).includes(storeDetails.affiliation._id)
  );
};