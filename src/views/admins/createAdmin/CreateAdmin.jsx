import React, { useEffect, useState } from "react";
import {
  CButton,
  CCol,
  CContainer,
  CForm,
  CInputGroup,
  CInputGroupText,
  CRow,
  CFormCheck,
  CFormSelect,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import { cilUser } from "@coreui/icons";
import { useNavigate } from "react-router-dom";
import LoadingGif from "src/components/alerts/loading/Loading";
import { useDispatch, useSelector } from "react-redux";
import { createAdmin, fetchAllMiddlewares } from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";

const schema = yup
  .object({
    name: yup.string().required(),
    email: yup.string().required(),
    role: yup.string().required(),
    password: yup.string().required("Password is required"),
    confirmPassword: yup
      .string()
      .required()
      .oneOf([yup.ref("password"), null], "Passwords must match"),
  })
  .required();

const CreateAdmin = () => {
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allMiddlewares } = useSelector((state) => state.middleware);

  useEffect(() => {
    dispatch(fetchAllMiddlewares({}));
  }, [dispatch]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const onSubmit = async (data) => {
    // dispatch(createAdmin(data));
    const res = await dispatch(createAdmin(data)).unwrap();
    if (res?.success) {
      navigate("/admins/all-admins");
    }
  };

  return (
    <CContainer fluid>
      <CRow className="justify-content-center">
        <CCol md={9} lg={7} xl={6}>
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <h4>Create new Admin</h4>
            <InputField
              state={"name"}
              title={"user Name"}
              type={"text"}
              setState={register}
              error={errors.name?.message}
            />
            <InputField
              state={"email"}
              title={"email"}
              type={"text"}
              setState={register}
              error={errors.email?.message}
            />

            <InputField
              state={"password"}
              title={"password"}
              type={showPassword ? "text" : "password"}
              setState={register}
              error={errors.password?.message}
            />
            <InputField
              state={"confirmPassword"}
              title={"confirm Password"}
              type={showPassword ? "text" : "password"}
              setState={register}
              error={errors.confirmPassword?.message}
            />
            <CInputGroup className="mb-3 position-relative">
              <CInputGroupText>
                <CIcon icon={cilUser} />
              </CInputGroupText>
              <CFormSelect
                aria-label="Default select example"
                {...register("role")}
                required
              >
                <option>Select one role</option>
                <option value={"super admin"}>Super Admin</option>
                {allMiddlewares?.map((data) => (
                  <option key={data.name} value={data.name}>
                    {data.name}
                  </option>
                ))}
              </CFormSelect>
              <p
                style={{ bottom: -18, right: 0 }}
                className="text-danger position-absolute"
              >
                {errors?.role?.message}
              </p>
            </CInputGroup>

            <CInputGroup className="mb-4  ">
              <CFormCheck
                onChange={(e) => {
                  if (e.target.checked) {
                    setShowPassword(!showPassword);
                  } else {
                    setShowPassword(!showPassword);
                  }
                }}
                id="flexCheckDefault"
                label={showPassword ? "hide password" : "show password"}
              />
            </CInputGroup>
            <div className="d-flex">
              <CButton
                type="reset"
                onClick={() => navigate("/admins/all-admins")}
                className="text-light ms-auto mx-1"
                color="danger"
              >
                Cancel
              </CButton>
              <CButton
                type="submit"
                className="text-light me-auto mx-1"
                color="success"
              >
                Create
              </CButton>
            </div>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
};

export default CreateAdmin;
