import {
  <PERSON><PERSON>,
  <PERSON>ontainer,
  CRow,
  CTable,
  CTableBody,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";
import { CSmartPagination, CTableDataCell } from "@coreui/react-pro";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import ReadMore from "src/components/text/ReadMore";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import { fetchAllAdminsList, fetchAllAdminsLogs } from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AdminLogs() {
  const dispatch = useDispatch();
  const { adminLogs, page, pages, pageSize, loading } = useSelector(
    (state) => state.adminLogs
  );

  const { adminList } = useSelector((state) => state.admin);

  useEffect(() => {
    dispatch(fetchAllAdminsList());
    dispatch(fetchAllAdminsLogs({}));
  }, [dispatch]);
  const handleSearch = async (search) => {
    dispatch(fetchAllAdminsLogs({ search }));
  };
  const handleSelectAdmin = async (values) => {
    dispatch(fetchAllAdminsLogs({ admin: values?.value }));
  };

  const handlePagination = async (page) => {
    dispatch(fetchAllAdminsLogs({ page }));
  };

  return (
    <CContainer fluid>
      <CRow className="">
        <CCol className=" my-2 " sm={6} xs={12} md={4} lg={4}>
          <SearchAndSelect
            array={adminList}
            handleSelectedValue={handleSelectAdmin}
            placeholder={"Select Admin..."}
          />
        </CCol>
      </CRow>
      <CRow>
        <CCol sm={6} xs={12} md={4} lg={4}>
          <div className="w-100">
            <input
              type="text"
              onChange={(e) => handleSearch(e.target.value)}
              className="p-1 w-100 px-2 rounded border"
              placeholder="Search..."
            />
          </div>
        </CCol>
        <CCol
          sm={6}
          xs={12}
          md={4}
          lg={4}
          className="ms-auto d-flex justify-content-end"
        >
          <CSmartPagination
            activePage={page}
            pages={pages}
            onActivePageChange={handlePagination}
          />
        </CCol>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>No</CTableHeaderCell>
              <CTableHeaderCell>Admin</CTableHeaderCell>
              <CTableHeaderCell>log</CTableHeaderCell>
              <CTableHeaderCell>Create At</CTableHeaderCell>
              <CTableHeaderCell>time</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              adminLogs?.map((item) => (
                <CTableRow key={item._id} className="">
                  <CTableDataCell>
                    {" "}
                    <p> {item?.uid}</p>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    {" "}
                    <p>{item.admin?.name}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    {" "}
                    <ReadMore text={item?.log} />
                  </CTableDataCell>
                  <CTableDataCell>
                    {" "}
                    <p>{dateFormatter(item.createdAt)} </p>
                  </CTableDataCell>
                  <CTableDataCell>
                    {" "}
                    <p> {timeFormatter(item.createdAt)}</p>{" "}
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
    </CContainer>
  );
}

export default AdminLogs;
