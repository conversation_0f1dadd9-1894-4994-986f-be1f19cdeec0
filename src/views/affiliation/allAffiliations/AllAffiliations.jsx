import { cilPencil, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import { deleteAffiliation, fetchAllAffiliations } from "src/redux/features";

function AllAffiliations() {
  const [admin, setAdmin] = useState(null);
  const [search, setSearch] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allAffiliations, page, pages, pageSize, loading } = useSelector(
    (state) => state.affiliation,
  );
  useEffect(() => {
    const adminInfo = localStorage.getItem("admin");
    if (adminInfo) {
      setAdmin(JSON.parse(adminInfo));
    }
    dispatch(fetchAllAffiliations({}));
  }, [dispatch]);

  const handleDisable = async (affiliationId) => {
    dispatch(deleteAffiliation(affiliationId));
  };
  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllAffiliations({ search: value }));
  };

  const handlePagination = async (value) => {
    dispatch(fetchAllAffiliations({ page: value }));
  };
  return (
    <CContainer fluid>
       <PaginationComponent page={page} pages={pages} handlePagination={handlePagination} />
      <div className="d-flex ">
        <div className="">
          <input
            type="text"
            onChange={(e) => handleSearch(e.target.value)}
            className="my-2 p-1 px-2 rounded border"
            placeholder="Search..."
          />
        </div>
        <div className="ms-auto">
          <button
            type="button"
            onClick={() => navigate("/affiliation/create-affiliation")}
            className=" my-2 btn btn-primary mx-auto"
          >
            Create Affiliation
          </button>
        </div>
      </div>
      <CTable
        align="middle"
        className="mb-0 border"
        hover
        striped
        bordered
        borderColor="secondary"
        responsive
      >
        <CTableHead color="dark">
          <CTableRow>
            <CTableHeaderCell>
              UID
            </CTableHeaderCell>
            <CTableHeaderCell>Affiliation Name</CTableHeaderCell>
            <CTableHeaderCell> API Name</CTableHeaderCell>
            <CTableHeaderCell> API Key</CTableHeaderCell>
            <CTableHeaderCell>Created By</CTableHeaderCell>
            <CTableHeaderCell>Added At</CTableHeaderCell>
            <CTableHeaderCell>Actions</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {!loading && allAffiliations?.map((item, index) => (
            <CTableRow
              v-for="item in tableItems"
              color={item?.active ? "" : "danger"}
              key={item._id}
            >
              <CTableDataCell className="text-center">
                <p>{item.uid}</p>
              </CTableDataCell>
              <CTableDataCell>
                <div>{item.name}</div>
              </CTableDataCell>
              <CTableDataCell className="">
                <div>{item.apiName}</div>
              </CTableDataCell>
              <CTableDataCell className="">
                <div>{item.apiKey}</div>
              </CTableDataCell>
              <CTableDataCell>
                <div className="small text-medium-emphasis">
                  {item?.createdBy?._id === admin?._id ? "YOU" : item?.createdBy?.name}
                </div>
              </CTableDataCell>
              <CTableDataCell>
                <div className="small text-medium-emphasis">
                  {dateFormatter(item.createdAt)}
                </div>
              </CTableDataCell>
              <CTableDataCell>
                <CTooltip content="edit affiliation details">
                  <button
                    type="button"
                    className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                    onClick={() => navigate(`/affiliation/update/${item._id}`)}
                  >
                    <CIcon icon={cilPencil} />
                  </button>
                </CTooltip>
                <CTooltip content="delete affiliation">
                  <button
                    type="button"
                    className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                    onClick={() => handleDisable(item._id)}
                  >
                    <CIcon icon={cilTrash} />
                  </button>
                </CTooltip>
              </CTableDataCell>
            </CTableRow>
          ))}
        </CTableBody>
      </CTable>
      {loading && <LoadingComponent/>}
      <PaginationComponent page={page} pages={pages} handlePagination={handlePagination} />
    </CContainer>
  );
}

export default AllAffiliations;
