import {
  <PERSON><PERSON>on,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ow,
} from "@coreui/react";
import React from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";
import { createAffiliation } from "src/redux/features";
import { useDispatch } from "react-redux";
const schema = yup
  .object({
    name: yup.string().required("affiliation name is a required field"),
    apiName: yup.string().required(" api name is a required field"),
    apiKey: yup.string().required(" api key is a required field"),
  })
  .required();

function CreateAffiliation() {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const onSubmit = (data) => {
    dispatch(createAffiliation(data));
  };

  return (
    <CContainer>
      <CRow className="justify-content-center">
        <h2 className="text-center">Create new Affiliation</h2>
        <CCol md={9} lg={7} xl={6}>
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <InputField
              state={"name"}
              title={"Affiliation Name"}
              type={"text"}
              setState={register}
              error={errors?.name?.message}
            />
            <InputField
              state={"apiName"}
              title={"api Name"}
              type={"text"}
              setState={register}
              error={errors?.apiName?.message}
            />
            <InputField
              state={"apiKey"}
              title={"api key"}
              type={"text"}
              setState={register}
              error={errors?.apiKey?.message}
            />
            <div className="mx-auto d-flex mt-3">
              <CButton
                onClick={() => navigate("/affiliation/all-affiliations")}
                className="text-white ms-auto mx-1"
                type="reset"
                color="danger"
              >
                Cancel
              </CButton>
              <CButton
                className="text-white me-auto mx-1"
                type="submit"
                color="success"
              >
                Create
              </CButton>
            </div>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default CreateAffiliation;
