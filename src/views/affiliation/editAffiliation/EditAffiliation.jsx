import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, CRow } from "@coreui/react";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";
import { fetchAffiliationDetails, updateAffiliation } from "src/redux/features";

const schema = yup
  .object({
    name: yup.string().required(),
    apiName: yup.string().required(" api name is a required field"),
    apiKey: yup.string().required(" api key is a required field"),
  })
  .required();

function EditAffiliation() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { affiliationId } = useParams();
  const { affiliationDetails } = useSelector((state) => state.affiliation);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const onSubmit = (data) => {
    const payload = { ...data };
    dispatch(
      updateAffiliation({ payload, affiliationId: affiliationDetails._id })
    );
  };
  useEffect(() => {
    dispatch(fetchAffiliationDetails(affiliationId));
  }, [affiliationId, dispatch]);

  useEffect(() => {
    if (affiliationDetails) {
      reset(
        {
          name: affiliationDetails.name,
          apiName: affiliationDetails.apiName,
          apiKey: affiliationDetails.apiKey,
        },
        {
          keepErrors: true,
          keepDirty: true,
        }
      );
    }
  }, [affiliationDetails, reset]);

  return (
    <div>
      <CContainer>
        <CRow className="justify-content-center">
          <CCol md={9} lg={7} xl={6}>
            <CForm onSubmit={handleSubmit(onSubmit)}>
              <h2>Update Affiliation</h2>
              <InputField
                state={"name"}
                title={"Affiliation Name"}
                type={"text"}
                setState={register}
                error={errors?.name?.message}
              />
              <InputField
                state={"apiName"}
                title={"api Name"}
                type={"text"}
                setState={register}
                error={errors?.apiName?.message}
              />
              <InputField
                state={"apiKey"}
                title={"api key"}
                type={"text"}
                setState={register}
                error={errors?.apiKey?.message}
              />
              <div className="mx-auto d-flex mt-3">
                <CButton
                  onClick={() => navigate("/affiliation/all-affiliations")}
                  className="text-white ms-auto mx-1"
                  type="reset"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  className="text-white me-auto mx-1"
                  type="submit"
                  color="success"
                >
                  update
                </CButton>
              </div>
            </CForm>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
}

export default EditAffiliation;
