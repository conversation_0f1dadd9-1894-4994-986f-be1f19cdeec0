import { cilPenAlt, cilThumbDown, cilThumbUp, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeader<PERSON>ell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import {
  dateDeferenceFormatter,
  dateFormatter,
} from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteEarning,
  fetchAllAffiliationsList,
  fetchAllAutoTracked,
  fetchAllEarnings,
  fetchAllStoresList,
  trackedToCancelEarning,
  trackedToConfirmEarning,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import ReadMore from "src/components/text/ReadMore";
import { useNavigate } from "react-router-dom";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AutoTrackedEarnings() {
  const [status, setStatus] = useState("pending");
  const [store, setStore] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [search, setSearch] = useState("");
  const [affiliation, setAffiliation] = useState("");
  // sort
  const [visible, setVisible] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);

  const {
    allAutoTracked: allEarnings,
    page,
    pages,
    loading,
  } = useSelector((state) => state.earnings);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllAutoTracked({ status }));
  }, [dispatch]);

  const handlePagination = async (value) => {
    dispatch(fetchAllAutoTracked({ status, page: value }));
  };

  const alertDelete = (earningId) => {
    setVisible(true);
    setDeleteId(earningId);
  };

  const handleApproveEarning = async (earningId) => {
    dispatch(trackedToConfirmEarning(earningId));
  };
  const handleCancelEarning = async (earningId) => {
    dispatch(trackedToCancelEarning(earningId));
  };
  const handleDeleteEarnings = async () => {
    dispatch(deleteEarning(deleteId));
  };

  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
    dispatch(
      fetchAllAutoTracked({ affiliation: values?.value, store, status })
    );
  };
  const handleSelectStores = (values) => {
    setStore(values?.value);
    dispatch(
      fetchAllAutoTracked({ store: values?.value, affiliation, status })
    );
  };
  const handleSelectStatus = (values) => {
    setStatus(values?.value);
    dispatch(
      fetchAllAutoTracked({ status: values?.value, affiliation, store })
    );
  };

  return (
    <CContainer className="">
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow></CRow>
      <CRow
        className="my-2"
        style={{
          marginBottom: 30,
        }}
      >
        <CCol md={4}></CCol>
      </CRow>
      <CRow className="">
        <CTable align="middle" className=" border" striped hover bordered>
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>actions</CTableHeaderCell>
              <CTableHeaderCell className="">ID</CTableHeaderCell>
              <CTableHeaderCell>User Id/User </CTableHeaderCell>
              <CTableHeaderCell>Order Id </CTableHeaderCell>
              <CTableHeaderCell>Click Id </CTableHeaderCell>
              <CTableHeaderCell>Referred By</CTableHeaderCell>
              <CTableHeaderCell>Store Name</CTableHeaderCell>
              <CTableHeaderCell>Affiliation</CTableHeaderCell>
              <CTableHeaderCell>Sale Amount</CTableHeaderCell>
              <CTableHeaderCell>Amount Got</CTableHeaderCell>
              <CTableHeaderCell>Amount To Give</CTableHeaderCell>
              <CTableHeaderCell>Orders Count/ID</CTableHeaderCell>
              <CTableHeaderCell>Added Date</CTableHeaderCell>
              <CTableHeaderCell>Transaction Date</CTableHeaderCell>
              <CTableHeaderCell>Tracking Time</CTableHeaderCell>
              <CTableHeaderCell>Notes</CTableHeaderCell>
              <CTableHeaderCell>Remarks</CTableHeaderCell>
              <CTableHeaderCell>Earnings Type</CTableHeaderCell>
              <CTableHeaderCell>Status</CTableHeaderCell>
              <CTableHeaderCell>Admin</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allEarnings?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems"
                  className="mx-2"
                  key={index}
                >
                  <CTableDataCell>
                    <CTooltip content={"Edit Earnings"}>
                      <button
                        type="button"
                        style={{ height: "2rem", fontSize: "12px" }}
                        className="border rounded shadow text-info px-2  m-1 pt-1"
                        onClick={(e) => navigate(`/earnings/edit/${item._id}`)}
                      >
                        {" "}
                        <CIcon icon={cilPenAlt} />
                      </button>
                    </CTooltip>
                    <CTooltip content={"Delete Earnings"}>
                      <button
                        type="button"
                        style={{ height: "2rem", fontSize: "12px" }}
                        className="border rounded shadow text-danger px-2  m-1 pt-1"
                        onClick={() => alertDelete(item._id)}
                      >
                        {" "}
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div className="">{item?.uid} </div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">
                      {item?.user?.uid}/{item?.user?.email}{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.orderUniqueId}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.click?.referenceId ?? ""}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.offer?.title}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.store?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.affiliation?.uid}/{item?.affiliation?.name}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.saleAmount}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.amountGot}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.amount}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.orderCount}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell className="fs-sm">
                    <div>
                      <small>
                        {dateFormatter(item?.purchaseDate ?? item.createdAt)}
                      </small>
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-center ">
                      {dateDeferenceFormatter(
                        item?.createdAt,
                        item?.trackingTime
                      )}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.notes} />{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.remarks} />{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-capitalize">{item?.earningsType}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="text-capitalize">
                      {item?.status?.replace(/_/g, " ")}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.createdBy?.name}</div>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete earnings ?"}
        setState={handleDeleteEarnings}
        setVisible={setVisible}
        visible={visible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AutoTrackedEarnings;
