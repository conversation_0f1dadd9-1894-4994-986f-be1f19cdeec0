import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON>ol,
  CContainer,
  CForm,
  CInputGroup,
  CModal,
  CModalBody,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilCloudUpload,
  cilExternalLink,
  cilPencil,
  cilPeople,
  cilTrash,
  cilWifiSignal4,
  cilThumbUp,
} from "@coreui/icons";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import { useNavigate } from "react-router-dom";
import { CSmartPagination } from "@coreui/react-pro";
import toast from "react-hot-toast";
import Warning from "src/components/alerts/Warning/Warning";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteCategory,
  fetchAllCategories,
  handleOpenModal,
  updateCategory,
  updateCategoryPriority,
  updateCategoryTopStatus,
  updateCategoryTrendingPriority,
  updateCategoryTrendingStatus,
} from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";
import ImageUploader from "src/components/fileManagers/imageUploader";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";
import SearchAndSelect from "src/components/select/SearchAndSelect";

const AllCategories = () => {
  const [category, setCategory] = useState("");
  const [search, setSearch] = useState("");
  const [show, setShow] = useState(false);
  const [status, setStatus] = useState("all");
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { allCategory, loading, page, pages } = useSelector(
    (state) => state.category
  );
  useEffect(() => {
    dispatch(fetchAllCategories({}));
  }, [dispatch]);

  const handleDeleteStatus = async (categoryId) => {
    dispatch(deleteCategory(categoryId));
  };

  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(
      fetchAllCategories({ search: value, status: status?._id, page: 1 })
    );
  };

  const handleSelectStatus = (values) => {
    setStatus({
      _id: values?.value,
      label: values?.label,
    });
    dispatch(fetchAllCategories({ status: values?.value, page: 1, search }));
  };
  const handlePagination = async (value) => {
    dispatch(fetchAllCategories({ page: value }));
  };

  const handleUpdate = (item) => {
    setCategory(item);
    setShow(true);
  };

  const handleTrendingPriority = (categoryId, priorityValue) => {
    const priority = priorityValue ? priorityValue : 0;
    dispatch(updateCategoryTrendingPriority({ categoryId, priority }));
  };

  const handlePriority = (categoryId, priorityValue) => {
    const priority = priorityValue ? priorityValue : 0;
    dispatch(updateCategoryPriority({ categoryId, priority }));
  };

  // Initialize a ref to hold input refs for each item
  const inputRefs = useRef([]);

  // Ensure inputRefs is filled with refs only once
  if (inputRefs.current.length !== allCategory.length) {
    // Clear out any existing refs and add new ones
    inputRefs.current = Array(allCategory.length)
      .fill()
      .map((_, index) => inputRefs.current[index] || React.createRef());
  }

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="d-flex mt-10 mb-5">
          <div className="d-flex flex-column justify-content-end">
            <label htmlFor="">Search</label>
            <input
              type="text"
              value={search}
              onChange={(e) => handleSearch(e.target.value)}
              className="p-1 px-2 rounded border"
              placeholder="Search..."
            />
          </div>
          <CCol md={4} className="mx-2 d-flex flex-column justify-content-end">
            <label htmlFor="">Status</label>
            <SearchAndSelect
              array={[
                {
                  name: "All",
                  _id: "all",
                },
                { name: "Top", _id: "top" },
              ]}
              defaultValue={status}
              handleSelectedValue={handleSelectStatus}
              placeholder={"Select Status..."}
            />
          </CCol>
          <div className="ms-auto">
            <button
              type="button"
              onClick={() => navigate("/category/create-category")}
              className=" my-2 btn btn-primary mx-auto"
            >
              Create Category
            </button>
            {/* <img alt="" src="" /> */}
          </div>
        </div>
      </CRow>

      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center">
                <CIcon icon={cilPeople} />
              </CTableHeaderCell>
              <CTableHeaderCell>Images</CTableHeaderCell>
              <CTableHeaderCell>Category Name</CTableHeaderCell>
              {/* <CTableHeaderCell>Trending Category</CTableHeaderCell> */}

              <CTableHeaderCell>Set as top</CTableHeaderCell>
              <CTableHeaderCell>Top Priority</CTableHeaderCell>
              {/* <CTableHeaderCell>No:of Sub Categories</CTableHeaderCell> */}
              <CTableHeaderCell>created by</CTableHeaderCell>
              <CTableHeaderCell>created at</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allCategory?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems "
                  color={item?.active ? "" : "danger"}
                  className=""
                  key={item._id}
                >
                  <CTableDataCell className="text-center">
                    <div>{item?.uid}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({ image: item?.image?.secureUrl })
                        )
                      }
                      width={50}
                      src={item?.image?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.name}</div>
                  </CTableDataCell>
                  {/* <CTableDataCell>
                    <div>
                      <input
                        className="border rounded  mx-auto p-2"
                        value={item.trendingPriority}
                        onChange={(e) =>
                          handleTrendingPriority(item._id, e.target.value)
                        }
                      />
                    </div>
                  </CTableDataCell> */}

                  <CTableDataCell>
                    <CTooltip
                      content={`${item.isTop ? "remove from" : "make it"} top`}
                    >
                      <button
                        type="button"
                        className={` ${
                          item?.isTop ? "text-info" : "text-danger"
                        }  border rounded shadow px-2 py-1 mx-1`}
                        onClick={() =>
                          dispatch(updateCategoryTopStatus(item._id))
                        }
                      >
                        <CIcon icon={cilThumbUp} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                  <CTableDataCell>
                    <input
                      // type="number"
                      type="decimal"
                      step="0.01"
                      className="w-75"
                      defaultValue={item.priority || 0} // Set default value
                      ref={inputRefs.current[index]} // Attach ref to the input
                    />
                    <button
                      type="button"
                      className=" my-2 btn btn-primary btn-xs  btn-sm "
                      onClick={() => {
                        const updatedPriority =
                          inputRefs.current[index].current.value; // Read value from the corresponding input
                        handlePriority(item._id, updatedPriority); // Call the function with item ID and priority
                      }}
                    >
                      Update
                    </button>
                  </CTableDataCell>

                  {/* <CTableDataCell>
                  <div>{item.subCategories?.length}</div>
                </CTableDataCell> */}
                  <CTableDataCell>
                    <div>{item?.createdBy?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item.createdAt)}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <CTooltip
                      content={`${
                        item.trending ? "remove from" : "make it"
                      } trending`}
                    >
                      <button
                        type="button"
                        className={` ${
                          item.trending ? "text-info" : "text-danger"
                        }  border rounded shadow px-2 py-1 mx-1`}
                        onClick={() =>
                          dispatch(updateCategoryTrendingStatus(item._id))
                        }
                      >
                        <CIcon icon={cilWifiSignal4} />
                      </button>
                    </CTooltip>
                    {/* <CTooltip
                      content={`${item.isTop ? "remove from" : "make it"} top`}
                    >
                      <button
                        type="button"
                        className={` ${
                          item?.isTop ? "text-info" : "text-danger"
                        }  border rounded shadow px-2 py-1 mx-1`}
                        onClick={() =>
                          dispatch(updateCategoryTopStatus(item._id))
                        }
                      >
                        <CIcon icon={cilThumbUp} />
                      </button>
                    </CTooltip> */}
                    <CTooltip content="view sub categories">
                      <button
                        type="button"
                        className=" text-info  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          item.subCategories?.length !== 0 &&
                          navigate(`/category/all-sub-category/${item._id}`)
                        }
                      >
                        <CIcon icon={cilExternalLink} />
                      </button>
                    </CTooltip>
                    <CTooltip content="edit category">
                      <button
                        type="button"
                        className=" text-success  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleUpdate(item)}
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content="delete category">
                      <button
                        type="button"
                        className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleDeleteStatus(item._id)}
                      >
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <UpdateModal
        visible={show}
        setVisible={setShow}
        category={category}
        dispatch={dispatch}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
};

export default AllCategories;

const schema = yup
  .object({
    name: yup.string().required(),
  })
  .required();

const UpdateModal = ({ visible, setVisible, category, dispatch }) => {
  const [image, setImage] = useState("");
  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    setImage(category.image);
    reset({ name: category?.name }, { keepErrors: true, keepDirty: true });
  }, [category, reset]);

  const onSubmit = (data) => {
    if (!image) {
      return toast.custom(<Warning message={"please upload any image"} />);
    }
    const payload = {
      ...data,
      image,
    };
    dispatch(updateCategory({ categoryId: category._id, payload }));
  };

  return (
    <CModal visible={visible}>
      <CModalBody className="border m-3 rounded">
        <h6 className="text-center mt-2 mb-4">Update Category</h6>
        <CForm onSubmit={handleSubmit(onSubmit)}>
          <CRow className="mb-2">
            <InputField
              state={"name"}
              title={"Category Name"}
              type={"text"}
              setState={register}
              error={errors?.name?.message}
            />
            <CInputGroup className="my-3 border shadow rounded">
              {image && (
                <img
                  className="mx-auto w-50"
                  src={image?.secureUrl}
                  alt="category logo"
                />
              )}
            </CInputGroup>
            <CInputGroup className="my-3 d-flex justify-content-center">
              <ImageUploader
                keyword={"Upload Image"}
                setFile={setImage}
                icon={cilCloudUpload}
                accept={".webp, .jpg, .jpeg, .png, .svg "}
              />
            </CInputGroup>
          </CRow>
          <div className="d-flex justify-content-center">
            <button
              type="reset"
              className=" mx-1 mt-3 text-white border rounded bg-danger px-4 py-1"
              onClick={() => {
                setVisible(false);
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              className=" mx-1 mt-3 text-white border rounded bg-success px-4 py-1"
            >
              Update
            </button>
          </div>
        </CForm>
      </CModalBody>
    </CModal>
  );
};
