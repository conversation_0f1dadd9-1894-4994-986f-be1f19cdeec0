import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, CRow } from "@coreui/react";
import React, { useState } from "react";
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import Warning from "src/components/alerts/Warning/Warning";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";
import ImageUploader from "src/components/fileManagers/imageUploader";
import { createCategory } from "src/redux/features";
import { cilCloudUpload } from "@coreui/icons";

const schema = yup
  .object({
    name: yup.string().required(),
  })
  .required();

function CreateCategory() {
  const [image, setImage] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const onSubmit = async (data) => {
    if (!image) {
      return toast.custom(<Warning message={"please upload any image"} />);
    }
    const payload = {
      ...data,
      image,
    };
    const resData = await dispatch(createCategory(payload)).unwrap();
    if (resData?.success) {
      navigate("/category");
    }
    // dispatch(createCategory(payload));
  };

  return (
    <CContainer>
      <CRow className="justify-content-center">
        <h2 className="text-center">Create new Category</h2>
        <CCol md={9} lg={8} xl={6} className="border rounded shadow pb-3">
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <InputField
              state={"name"}
              title={"Category Name"}
              type={"text"}
              setState={register}
              error={errors?.name?.message}
            />
            <div
              className=" d-flex justify-content-center border shadow rounded my-3 py-2 w-75 mx-auto "
              style={{ height: "10rem" }}
            >
              {image && (
                <img
                  className="mx-auto h-100  "
                  src={image?.secureUrl}
                  alt="category logo"
                />
              )}
            </div>
            <div className=" d-flex justify-content-center ">
              <ImageUploader
                setFile={setImage}
                icon={cilCloudUpload}
                keyword={"Upload Image"}
                accept={".webp, .jpg, .jpeg, .png, .svg "}
              />
            </div>

            <div className="mx-auto d-flex mt-3">
              <CButton
                className="text-white ms-auto mx-1"
                onClick={() => navigate("/category")}
                type="reset"
                color="danger"
              >
                Cancel
              </CButton>
              <CButton
                className="text-white me-auto mx-1"
                type="submit"
                color="success"
              >
                Create{" "}
              </CButton>
            </div>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default CreateCategory;
