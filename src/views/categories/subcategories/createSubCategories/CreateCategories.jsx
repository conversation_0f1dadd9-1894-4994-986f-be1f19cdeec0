import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ontainer,
  <PERSON>orm,
  CInputGroup,
  CRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import MyCkEditor from "src/components/CkEditor/Editor";
import ImageUploader from "src/components/fileManagers/imageUploader";
import InputField from "src/components/inputs/InputField";
import { createSubCategory, fetchAllCategoriesList } from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import toast from "react-hot-toast";
import Warning from "src/components/alerts/Warning/Warning";
import { cilCloudUpload } from "@coreui/icons";
import SearchAndSelect from "src/components/select/SearchAndSelect";

const schema = yup
  .object({
    name: yup.string().required(),
  })
  .required();

function CreateSubCategories() {
  const [image, setImage] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allCategoryList } = useSelector((state) => state.category);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    dispatch(fetchAllCategoriesList());
  }, [dispatch]);

  const onSubmit = async (data) => {
    if (!image) {
      return toast.custom(<Warning message={"please select any image"} />);
    }
    if (!description) {
      return toast.custom(<Warning message={"please add description"} />);
    }
    if (!category) {
      return toast.custom(<Warning message={"please select Category"} />);
    }
    const payload = {
      ...data,
      category,
      image,
      description,
    };
    const res = await dispatch(createSubCategory(payload)).unwrap();
    if (res.success) {
      navigate("/category/all-sub-category/null");
    }
  };
  const handleSelectCategory = (values) => {
    setCategory(values?.value);
  };
  return (
    <div>
      <CContainer fluid>
        <CRow className="">
          <h3 className="text-center">Create new Sub Category</h3>
          <CCol md={10} lg={10} xl={6} className=" shadow rounded p-3 mx-auto">
            <CForm onSubmit={handleSubmit(onSubmit)}>
              <InputField
                state={"name"}
                title={"Sub Category Name"}
                type={"text"}
                setState={register}
                error={errors?.name?.message}
              />
              <CInputGroup className="my-3">
                <p className="my-2">Description</p>
                <MyCkEditor setData={setDescription} />
              </CInputGroup>
              <CInputGroup className="my-3">
                <p className="my-2">Select Category</p>
                <SearchAndSelect
                  array={allCategoryList}
                  handleSelectedValue={handleSelectCategory}
                  placeholder={"Select Category..."}
                />
              </CInputGroup>

              <p className="my-2">Category Logo</p>
              <div
                className=" d-flex justify-content-center border shadow rounded my-3 py-2 w-75 mx-auto "
                style={{ height: "10rem" }}
              >
                {image && (
                  <img
                    className="mx-auto h-100  "
                    src={image?.secureUrl}
                    alt="category logo"
                  />
                )}
              </div>
              <div className=" d-flex justify-content-center ">
                <ImageUploader
                  setFile={setImage}
                  icon={cilCloudUpload}
                  keyword={"Upload Image"}
                  accept={".webp, .jpg, .jpeg, .png, .svg"}
                />
              </div>
              <div className="mx-auto d-flex mt-3">
                <CButton
                  className="text-white ms-auto mx-1"
                  onClick={() => navigate("/category/all-sub-category/null")}
                  type="reset"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  className="text-white me-auto mx-1"
                  type="submit"
                  color="success"
                >
                  Create{" "}
                </CButton>
              </div>
            </CForm>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
}

export default CreateSubCategories;
