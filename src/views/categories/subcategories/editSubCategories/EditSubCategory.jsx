import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  <PERSON>orm,
  CInputGroup,
  CRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import Warning from "src/components/alerts/Warning/Warning";
import MyCkEditor from "src/components/CkEditor/Editor";
import ImageUploader from "src/components/fileManagers/imageUploader";
import InputField from "src/components/inputs/InputField";
import {
  fetchAllCategoriesList,
  fetchSubCategoryDetails,
  updateSubCategory,
} from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import { cilCloudUpload } from "@coreui/icons";

const schema = yup
  .object({
    name: yup.string().required(),
  })
  .required();
function EditSubCategory() {
  const [image, setImage] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState("");
  const [defaultCategory, setDefaultCategory] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { categoryId } = useParams();
  const { allCategoryList } = useSelector((state) => state.category);
  const { subCategoryDetails } = useSelector((state) => state.subCategory);

  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  console.log(subCategoryDetails);
  useEffect(() => {
    if (subCategoryDetails) {
      setImage(subCategoryDetails?.image);
      setCategory(subCategoryDetails?.category?._id);
      setDefaultCategory({
        label: subCategoryDetails?.category?.name,
        value: subCategoryDetails?.category?._id,
      });
      setDescription(subCategoryDetails?.description);
      reset(
        {
          name: subCategoryDetails?.name,
        },
        { keepDirty: true, keepErrors: true }
      );
    }
  }, [subCategoryDetails, reset]);

  useEffect(() => {
    dispatch(fetchAllCategoriesList());
    if (categoryId) {
      dispatch(fetchSubCategoryDetails(categoryId));
    }
  }, [dispatch, categoryId]);

  const onSubmit = async (data) => {
    if (!image) {
      return toast.custom(<Warning message={"please select any image"} />);
    }
    if (!description) {
      return toast.custom(<Warning message={"please add description"} />);
    }
    const payload = {
      name: data.name,
      category,
      image,
      description,
    };
    const res = await dispatch(
      updateSubCategory({ categoryId: subCategoryDetails._id, payload })
    ).unwrap();
    if (res.success) {
      navigate("/category/all-sub-category/null");
    }
  };

  const handleSelectCategory = (values) => {
    setCategory(values?.value);
  };

  return (
    <div>
      <CContainer fluid>
        <CRow className="justify-content-center">
          <h3 className="text-center">Edit Sub Category</h3>
          <CCol md={10} lg={10} xl={6} className=" shadow rounded p-3">
            <CForm onSubmit={handleSubmit(onSubmit)}>
              <InputField
                state={"name"}
                title={"user Name"}
                type={"text"}
                setState={register}
                error={errors?.name?.message}
              />
              <p className="my-2">Description</p>
              <CInputGroup className="my-3">
                <MyCkEditor content={description} setData={setDescription} />
              </CInputGroup>
              <CInputGroup className="my-3">
                <p className="my-2">Select Category</p>
                <SearchAndSelect
                  defaultValue={defaultCategory}
                  array={allCategoryList}
                  handleSelectedValue={handleSelectCategory}
                  placeholder={"Select Category..."}
                />
              </CInputGroup>
              <p className="my-2">Category Logo</p>
              <div
                className=" d-flex justify-content-center border shadow rounded my-3 py-2 w-75 mx-auto "
                style={{ height: "10rem" }}
              >
                {image && (
                  <img
                    className="mx-auto h-100  "
                    src={image?.secureUrl}
                    alt="category logo"
                  />
                )}
              </div>
              <div className=" d-flex justify-content-center ">
                <ImageUploader
                  setFile={setImage}
                  icon={cilCloudUpload}
                  keyword={"Upload Image"}
                  accept={".webp,.jpg,.jpeg,.png .svg"}
                />
              </div>

              <div className="mx-auto d-flex mt-3">
                <CButton
                  className="text-white ms-auto mx-1"
                  onClick={() => navigate("/category/all-sub-category/null")}
                  type="reset"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  className="text-white me-auto mx-1"
                  type="submit"
                  color="success"
                >
                  Update{" "}
                </CButton>
              </div>
            </CForm>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
}

export default EditSubCategory;
