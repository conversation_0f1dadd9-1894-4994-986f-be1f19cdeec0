import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ol,
  <PERSON>ontainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import {
  fetchAllAffiliationsList,
  fetchAllClicks,
  fetchAllStoresList,
} from "src/redux/features";

function AllClicks() {
  const [search, setSearch] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  const [userType, setUserType] = useState("all");

  // filter
  const [status, setStatus] = useState("");
  const [store, setStore] = useState("");
  const [affiliation, setAffiliation] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);
  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    const searchParams = new URLSearchParams(location.search);
    const searchParamValue = searchParams.get("searchParam");
    setSearch(searchParamValue);
    dispatch(
      fetchAllClicks({ search: searchParamValue ? searchParamValue : "" })
    );
  }, [dispatch]);

  const { page, pages, pageSize, loading, allClicks } = useSelector(
    (state) => state.clicks
  );

  const handlePagination = async (value) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete("searchParam");
    navigate(`${location.pathname}?${searchParams.toString()}`, {
      replace: true,
    });
    dispatch(
      fetchAllClicks({
        page: value,
        search: search ? search : "",
        store,
        affiliation,
        status,
        userType: userType ? userType : "",
        fromDate,
        toDate,
      })
    );
  };
  const handleSort = async (e) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete("searchParam");
    navigate(`${location.pathname}?${searchParams.toString()}`, {
      replace: true,
    });
    dispatch(
      fetchAllClicks({
        search,
        store,
        affiliation,
        status,
        userType,
        fromDate,
        toDate,
      })
    );
  };

  const handleResetFilter = async (e) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete("searchParam");
    navigate(`${location.pathname}?${searchParams.toString()}`, {
      replace: true,
    });
    setAffiliation("");
    setSearch("");
    setFromDate("");
    setStatus("");
    setStore("");
    setUserType("all");
    dispatch(fetchAllClicks({}));
  };

  const handleSearch = async (value) => {
    setSearch(value);
  };

  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
    // dispatch(fetchAllClicks({ affiliation: values?.value, status, store }));
  };

  const handleSelectStores = (values) => {
    setStore(values?.value);
    // dispatch(fetchAllClicks({ store: values?.value, affiliation, status }));
  };

  const handleSelectStatus = (values) => {
    setStatus(values?.value);
    // dispatch(fetchAllClicks({ status: values?.value, affiliation, store }));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <CCol xs={12} md={8} xl={8} className="mb-3">
          <input
            type="text"
            className="w-sm-100 w-md-25  border rounded  p-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow className="mb-3 mt-10">
        <CCol sm={12}>
          <p className="h6">User Type :</p>
        </CCol>
        <CCol sm={3}>
          <input
            type="radio"
            name="exampleRadio"
            id="offers_1"
            value="0"
            className="mx-1"
            onClick={() => {
              setUserType("all");
            }}
            checked={userType == "all"}
          />
          <label htmlFor="offers_1">All Users</label>
        </CCol>
        <CCol sm={3}>
          <input
            type="radio"
            name="exampleRadio"
            id="offers_1"
            value="0"
            className="mx-1"
            onClick={() => {
              setUserType("guest");
            }}
            checked={userType == "guest"}
          />
          <label htmlFor="offers_1">Guest Users</label>
        </CCol>
        <CCol className="" sm={3}>
          <input
            type="radio"
            name="exampleRadio"
            id="offers_2"
            onClick={() => setUserType("logged")}
            checked={userType == "logged"}
            className="mx-1"
            defaultChecked
          />
          <label htmlFor="offers_2">Logged in users</label>
        </CCol>
      </CRow>
      <CRow>
        <CCol xs={12} md={8} xl={8} className="mb-3">
          <p className="h6">Registration Date :</p>
          <div className="d-md-flex">
            <div>
              <label htmlFor="">Start Date</label>
              <input
                type="date"
                name=""
                className="border rounded  p-2 m-2"
                onChange={(e) => setFromDate(e.target.value)}
                placeholder=""
                id=""
              />
            </div>
            <div>
              <label htmlFor="">End Date</label>
              <input
                type="date"
                name=""
                className="border rounded  p-2 m-2"
                onChange={(e) => setToDate(e.target.value)}
                id=""
              />
            </div>
          </div>
        </CCol>
        <CRow>
          <CCol md={4}>
            <SearchAndSelect
              array={[
                { name: "Clicked", _id: "clicked" },
                { name: "Tracked", _id: "tracked" },
                { name: "Confirmed", _id: "confirmed" },
                { name: "Cancelled", _id: "cancelled" },
              ]}
              handleSelectedValue={handleSelectStatus}
              placeholder={"Select Status..."}
            />
          </CCol>
          <CCol md={4}>
            <SearchAndSelect
              array={allAffiliationsList}
              handleSelectedValue={handleSelectAffiliations}
              placeholder={"Select Partners..."}
            />
          </CCol>
          <CCol md={4}>
            <SearchAndSelect
              array={allStoresList}
              handleSelectedValue={handleSelectStores}
              placeholder={"Select Stores..."}
            />
          </CCol>
        </CRow>
        <CCol xs={12} className="text-end">
          <button
            className="border rounded border-danger shadow px-4 py-1 text-danger fw-bold  m-2"
            onClick={handleResetFilter}
          >
            CANCEL
          </button>
          <button
            className="border rounded border-primary shadow px-4 py-1 text-primary fw-bold  m-2"
            onClick={handleSort}
          >
            FILTER
          </button>
        </CCol>
      </CRow>
      <CRow>
        <CCol className="bg-white border py-3 rounded" md={12}>
          <CTable
            align="middle"
            className=" border "
            striped
            hover
            bordered
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell>Actions</CTableHeaderCell>
                <CTableHeaderCell>NO</CTableHeaderCell>
                <CTableHeaderCell>userId</CTableHeaderCell>
                <CTableHeaderCell>ClickId</CTableHeaderCell>
                <CTableHeaderCell>User</CTableHeaderCell>
                <CTableHeaderCell> User Email</CTableHeaderCell>
                <CTableHeaderCell>Store </CTableHeaderCell>
                <CTableHeaderCell>Offer Id</CTableHeaderCell>
                <CTableHeaderCell>Offer Title</CTableHeaderCell>
                <CTableHeaderCell>Offer Affiliation</CTableHeaderCell>
                <CTableHeaderCell>User IP</CTableHeaderCell>
                <CTableHeaderCell>City</CTableHeaderCell>
                <CTableHeaderCell>Device</CTableHeaderCell>
                <CTableHeaderCell>Date And Time</CTableHeaderCell>
                <CTableHeaderCell>User Notes</CTableHeaderCell>
                <CTableHeaderCell>Click Type</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allClicks?.map((item) => (
                  <CTableRow v-for="item in tableItems" key={item.uid}>
                    <CTableDataCell>
                      {item?.status != "clicked" ? (
                        <CButton
                          className="text-white "
                          color="success"
                          size="sm"
                          disabled
                        >
                          Approved
                        </CButton>
                      ) : item?.user ? (
                        <CButton
                          className=""
                          size="sm"
                          onClick={() =>
                            navigate(`/earnings/create/${item._id}/click`)
                          }
                        >
                          Approve
                        </CButton>
                      ) : (
                        <></>
                      )}
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div className="">{item?.uid}</div>{" "}
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.user?.uid}</div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.referenceId}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.user?.name}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="">{item?.user?.email} </div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.store?.name}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.offer?.uid}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.offer?.title}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.affiliation?.name}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.userIp} </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.userCity}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.device}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      {" "}
                      <div>
                        {dateFormatter(item?.createdAt)}{" "}
                        <small>{timeFormatter(item?.createdAt)}</small>{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.user?.userNotes}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div
                        className={`${
                          item.type == "express" &&
                          "border-success text-success"
                        } 
                      ${item.type == "rates" && "border-warning text-warning"}
                       ${
                         item.type == "offer" && "border-info text-info"
                       } text-uppercase  text-center border rounded p-1 shadow `}
                      >
                        {item?.type}
                      </div>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllClicks;
