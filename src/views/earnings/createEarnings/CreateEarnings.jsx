import { <PERSON><PERSON>, CContainer, CForm, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import {
  dateInDDMMYYYYFormat,
  dateInYYYYMMDDFormat,
} from "src/helperFunctions/dateFormatter";
import {
  createEarnings,
  fetchAllAffiliationsList,
  fetchAllStoresList,
  fetchClickDetails,
} from "src/redux/features";

import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import InputField from "src/components/inputs/InputField";
import MyCkEditor from "src/components/CkEditor/Editor";
import SearchAndSelectUsers from "src/components/select/SearchAndSelectUsers";

const schema = yup
  .object({
    name: yup.string().required(),
    user: yup.string().required(),
    userId: yup.string().required(),
    userIp: yup.string().required(),
    offer: yup.string(),
    referralPersonName: yup.string(),
    referralPerson: yup.string(),
    referralCommission: yup.number(),
    confirmDate: yup
      .string()
      .required()
      .typeError("approximate confirmation date is a required field!"),
    amountPromised: yup
      .number()
      .required()
      .typeError("amount promised by partner is a required field!"),
    saleAmount: yup
      .number()
      .required()
      .typeError("sale amount is a required field!"),
    categoryPercentOrAmount: yup
      .string()
      .typeError("category percentage/amount  is required field"),
    amountGot: yup
      .number()
      .required("amount got from partner is a required field!"),
    orderCount: yup.number().typeError("How many order  is a required field!"),
    cashbackAmount: yup.number().required(),
    orderUniqueId: yup.string(),
    trackingTime: yup.string().required(),
    earningsType: yup.string().required(),
    flipkart: yup.boolean().default(false),
    offerFromPartner: yup.string().required(),
  })
  .required();

function CreateEarnings() {
  const { clickId, earningsType } = useParams();
  const [store, setStore] = useState("");
  const [affiliation, setAffiliation] = useState("");
  const [advertiserInfo, setAdvertiserInfo] = useState("");
  const [otherInfo, setOtherInfo] = useState("");
  const [notes, setNotes] = useState("");
  const [remarks, setRemarks] = useState("");
  const [user, setUser] = useState("");

  const {
    register,
    watch,
    setValue,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);

  const { clickDetails } = useSelector((state) => state.clicks);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
  }, [dispatch]);

  useEffect(() => {
    if (clickId) {
      dispatch(fetchClickDetails(clickId));
    }
  }, [clickId, dispatch]);

  useEffect(() => {
    if (clickDetails) {
      setAffiliation(clickDetails?.affiliation?._id);
      setStore(clickDetails?.store?._id);
      setUser({
        label: clickDetails?.user?.name,
        value: clickDetails?.user?._id,
      });
      reset(
        {
          name: clickDetails?.user?.name,
          userId: clickDetails?.user?.uid,
          user: clickDetails?.user?._id,
          offer: clickDetails?.offer?.uid,
          userIp: clickDetails?.userIp
            ? clickDetails.userIp
            : "***************",
          referralPersonName: clickDetails?.referral?.name,
          referralPerson: clickDetails?.referral?._id,
          // trackingTime: dateInYYYYMMDDFormat(clickDetails?.createdAt),
          trackingTime: dateInDDMMYYYYFormat(clickDetails?.createdAt),
          // trackingTime: ,
          referralCommission: 0,
          earningsType,
        },
        { keepDirty: true, keepErrors: true }
      );
    }
  }, [clickDetails, reset]);

  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
  };
  const handleSelectStore = (values) => {
    setStore(values?.value);
  };
  const onSubmit = async (data) => {
    const payload = {
      ...data,
      click: clickId,
      offer: clickDetails?.offer?._id,
      store,
      affiliation,
      notes,
      advertiserInfo,
      otherInfo,
      remarks,
      user: user?.value,
    };
    const dataRes = await dispatch(createEarnings(payload)).unwrap();
    if (dataRes?.success) {
      navigate("/earnings");
    }
  };

  const handleSelectUser = (values) => {
    setUser({
      label: values?.label,
      value: values?.value,
    });
    // Update form fields with user details
    setValue("name", values?.label);
    setValue("userId", values?.uid);
  };

  // Watch for changes in fieldOne and fieldTwo
  const saleAmount = watch("saleAmount");
  const categoryPercentOrAmount = watch("categoryPercentOrAmount");

  // Use useEffect to update fieldThree based on changes in fieldOne and fieldTwo
  useEffect(() => {
    if (
      saleAmount &&
      categoryPercentOrAmount &&
      categoryPercentOrAmount.includes("%")
    ) {
      // Remove the '%' symbol from categorypercent
      const percentValue = parseFloat(categoryPercentOrAmount.replace("%", ""));

      // Logic to calculate the new value for fieldThree
      const newValue = (saleAmount * (percentValue / 100)).toFixed(2);
      setValue("cashbackAmount", newValue);
    }
  }, [saleAmount, categoryPercentOrAmount, setValue]);

  return (
    <CContainer fluid>
      <CRow className="justify-content-center">
        <CCol md={12} lg={10} xl={8} xxl={8}>
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <h4>Approve Orders (Create Earning)</h4>
            <p>
              match with payments received and approve this order by entering
              cashback amount.
            </p>
            <InputField
              title={"Flipkart Reward"}
              state={"flipkart"}
              setState={register}
              type={"checkbox"}
              error={errors?.flipkart?.message}
            />
            <InputField
              title={"User Id"}
              state={"userId"}
              setState={register}
              type={"text"}
              error={errors?.userId?.message}
              disable
            />
            <InputField
              title={"User Name"}
              state={"name"}
              setState={register}
              type={"text"}
              error={errors?.name?.message}
              disable
            />

            <CRow className="my-4">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto text-capitalize">User </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelectUsers
                  defaultValue={user}
                  handleSelectedValue={handleSelectUser}
                  placeholder="Select User..."
                  pageSize={50}
                />
              </CCol>
            </CRow>

            <CRow className="my-4">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto text-capitalize">Store </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  defaultValue={{
                    label: clickDetails?.store?.name,
                    value: clickDetails?.store?._id,
                  }}
                  array={allStoresList}
                  handleSelectedValue={handleSelectStore}
                  placeholder={"Select Store..."}
                />
              </CCol>
            </CRow>

            <InputField
              title={"offer Id"}
              state={"offer"}
              setState={register}
              type={"text"}
              error={errors?.offer?.message}
              disable
            />
            <InputField
              title={"IP address of User"}
              state={"userIp"}
              setState={register}
              type={"text"}
              error={errors?.userIp?.message}
              disable
            />
            {/* <InputField
              title={"Time of Order"}
              state={"trackingTime"}
              setState={register}
              type={"date"}
              error={errors?.trackingTime?.message}
            /> */}
            <InputField
              title={"Time of Order"}
              state={"trackingTime"}
              setState={register}
              type={"datetime-local"}
              error={errors?.trackingTime?.message}
            />

            <CRow className="my-4">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto text-capitalize">Affiliation </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  defaultValue={{
                    label: clickDetails?.affiliation?.name,
                    value: clickDetails?.affiliation?._id,
                  }}
                  array={allAffiliationsList}
                  handleSelectedValue={handleSelectAffiliations}
                  placeholder={"Select Affiliation..."}
                />
              </CCol>
            </CRow>

            <InputField
              title={"Amount got from partner"}
              state={"amountGot"}
              setState={register}
              type={"decimal"}
              step="0.01"
              error={errors?.amountGot?.message}
            />

            <CRow>
              <div className="mb-4">
                <div className="w-100 ">
                  <InputField
                    title={"Amount promised by partner"}
                    state={"amountPromised"}
                    setState={register}
                    type={"decimal"}
                    step="0.01"
                    error={errors?.amountPromised?.message}
                  />
                </div>
                <div
                  className="d-flex justify-content-end"
                  style={{ marginTop: "-25px" }}
                >
                  {" "}
                  {/* Removed mt-2 class */}
                  <button
                    type="button"
                    className="btn btn-info text-white btn-sm mx-1 text-sm"
                    onClick={() => {
                      window.open(
                        `/store-categories/all-store-categories?store=${store}`,
                        "_blank"
                      );
                    }}
                  >
                    View Categories
                  </button>
                  <button
                    type="button"
                    className="btn btn-info text-white btn-sm mx-1 text-sm"
                    onClick={() => {
                      window.open(
                        `/store-categories/category-histories?store=${store}`,
                        "_blank"
                      );
                    }}
                  >
                    View Category History
                  </button>
                </div>
              </div>
            </CRow>

            <InputField
              title={"Sale Amount"}
              state={"saleAmount"}
              setState={register}
              //type={"number"}
              type="decimal"
              step="0.01"
              error={errors?.saleAmount?.message}
            />

            <CRow className="my-4">
              <div className="mb-4">
                <div className="w-100">
                  <InputField
                    title={"What is the offer from partner"}
                    state={"offerFromPartner"}
                    setState={register}
                    type={"text"}
                    error={errors?.offerFromPartner?.message}
                  />
                </div>
                <div
                  className="d-flex justify-content-end"
                  style={{ marginTop: "-20px" }}
                >
                  <button
                    type="button"
                    onClick={() => {
                      const amountGot = parseFloat(getValues("amountGot"));
                      const saleAmount = parseFloat(getValues("saleAmount"));
                      if (
                        !isNaN(amountGot) &&
                        !isNaN(saleAmount) &&
                        saleAmount !== 0
                      ) {
                        const value = (amountGot * 100) / saleAmount;
                        setValue("offerFromPartner", value.toFixed(2));
                      } else {
                        console.error("Invalid input values");
                      }
                    }}
                    className="btn btn-info text-white btn-sm px-3"
                  >
                    Calculate Percent
                  </button>
                </div>
              </div>
            </CRow>

            <InputField
              title={"Unique Id of Order (Leave blank if mixing orders)"}
              state={"orderUniqueId"}
              setState={register}
              type={"text"}
              error={errors?.orderUniqueId?.message}
            />
            <CRow className="my-4">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto text-capitalize">Advertiser Info </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor setData={setAdvertiserInfo} />
              </CCol>
            </CRow>
            <CRow className="my-4">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto text-capitalize">Other Info </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor setData={setOtherInfo} />
              </CCol>
            </CRow>
            <InputField
              title={"Category Percentage/Amount"}
              state={"categoryPercentOrAmount"}
              setState={register}
              type={"text"}
              error={errors?.categoryPercentOrAmount?.message}
            />
            <InputField
              title={"How many other orders"}
              state={"orderCount"}
              setState={register}
              type="decimal"
              step="0.01"
              error={errors?.orderCount?.message}
            />
            <InputField
              title={"Cashback amount to user"}
              state={"cashbackAmount"}
              setState={register}
              type={"decimal"}
              error={errors?.cashbackAmount?.message}
              // disable={true}
            />
            <InputField
              title={"Approximate date of confirmation"}
              state={"confirmDate"}
              setState={register}
              type={"date"}
              error={errors?.confirmDate?.message}
            />
            <CRow className="my-4">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto text-capitalize">Notes</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor setData={setNotes} />
              </CCol>
            </CRow>
            <CRow className="my-4">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto text-capitalize">
                  Remarks (item name or gift voucher){" "}
                </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor setData={setRemarks} />
              </CCol>
            </CRow>
            <InputField
              title={"Referred Person"}
              state={"referralPersonName"}
              setState={register}
              type={"text"}
              error={errors?.referralPersonName?.message}
              disable
            />
            <InputField
              title={"Referral Commission"}
              state={"referralCommission"}
              setState={register}
              type={"text"}
              error={errors?.referralCommission?.message}
              disable={clickDetails?.referral ? false : true}
            />
            <CRow className=" ">
              <CCol md={6} className="text-end">
                <button
                  type="reset"
                  className="btn btn-danger text-white btn-sm px-5"
                >
                  Cancel
                </button>
              </CCol>
              <CCol md={6} className="text-start">
                <button
                  type="submit"
                  className="btn btn-success text-white btn-sm px-3"
                >
                  Approve Order
                </button>
              </CCol>
            </CRow>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default CreateEarnings;
