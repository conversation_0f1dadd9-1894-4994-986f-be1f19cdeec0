import { cilPenAlt, cilThumbDown, cilThumbUp, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeader<PERSON>ell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import {
  dateDeferenceFormatter,
  dateFormatter,
} from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteEarning,
  fetchAllAffiliationsList,
  fetchAllEarnings,
  fetchAllStoresList,
  trackedToCancelEarning,
  trackedToConfirmEarning,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import ReadMore from "src/components/text/ReadMore";
import { useNavigate } from "react-router-dom";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function PendingEarnings() {
  const [status, setStatus] = useState("pending");
  const [store, setStore] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [search, setSearch] = useState("");
  const [affiliation,setAffiliation] = useState("")
  // sort
  const [visible, setVisible] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);

  const { allEarnings, page, pages, loading } = useSelector(
    (state) => state.earnings
  );

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllEarnings({ status }));
  }, [dispatch]);

  const handlePagination = async (value) => {
    dispatch(fetchAllEarnings({ status, page: value }));
  };

  const alertDelete = (earningId) => {
    setVisible(true);
    setDeleteId(earningId);
  };

  const handleApproveEarning = async (earningId) => {
    dispatch(trackedToConfirmEarning(earningId));
  };
  const handleCancelEarning = async (earningId) => {
    dispatch(trackedToCancelEarning(earningId));
  };
  const handleDeleteEarnings = async () => {
    dispatch(deleteEarning(deleteId));
  };

  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
    dispatch(fetchAllEarnings({ affiliation: values?.value,store,status }));
  };
  const handleSelectStores = (values) => {
    setStore(values?.value);
    dispatch(fetchAllEarnings({ store: values?.value,affiliation,status }));
  };
  const handleSelectStatus = (values) => {
    setStatus(values?.value);
    dispatch(fetchAllEarnings({ status: values?.value,affiliation,store }));
  };

  return (
    <CContainer  className="">
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <CCol md={6}>
          <input
            type="text"
            className="w-50 border rounded py-1 px-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow className="mt-3">
        <CCol xs={12} className="mb-2">
          <p className="h6"> Date Range :</p>
          <div className="d-flex">
            <div>
              <label htmlFor="">Start Date</label>
              <input
                type="date"
                name=""
                className="border rounded  p-2 m-2"
                onChange={(e) => setStartDate(e.target.value)}
                placeholder=""
                id=""
              />
            </div>
            <div>
              <label htmlFor="">End Date</label>
              <input
                type="date"
                name=""
                className="border rounded  p-2 m-2"
                onChange={(e) => setEndDate(e.target.value)}
                id=""
              />
            </div>
          </div>
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol md={4}>
          <SearchAndSelect
            array={[
              { name: "Pending", _id: "pending" },
              { name: "Confirmed", _id: "confirmed" },
              { name: "Cancelled", _id: "cancelled" },
            ]}
            handleSelectedValue={handleSelectStatus}
            placeholder={"Select Status..."}
          />
        </CCol>
        <CCol md={4}>
          <SearchAndSelect
            array={allAffiliationsList}
            handleSelectedValue={handleSelectAffiliations}
            placeholder={"Select Partners..."}
          />
        </CCol>
        <CCol md={4}>
          <SearchAndSelect
            array={allStoresList}
            handleSelectedValue={handleSelectStores}
            placeholder={"Select Stores..."}
          />
        </CCol>
      </CRow>
      <CRow className="">
          <CTable
            align="middle"
            className=" border"
            striped
            hover
            bordered
            >
            <CTableHead color="dark">
              <CTableRow>
              <CTableHeaderCell>actions</CTableHeaderCell>
                <CTableHeaderCell className="">ID</CTableHeaderCell>
                <CTableHeaderCell>User Id/User </CTableHeaderCell>
                <CTableHeaderCell>Referred By</CTableHeaderCell>
                <CTableHeaderCell>Store Name</CTableHeaderCell>
                <CTableHeaderCell>Affiliation</CTableHeaderCell>
                <CTableHeaderCell>Sale Amount</CTableHeaderCell>
                <CTableHeaderCell>Amount Got</CTableHeaderCell>
                <CTableHeaderCell>Amount To Give</CTableHeaderCell>
                <CTableHeaderCell>Orders Count/ID</CTableHeaderCell>
                <CTableHeaderCell>Added Date</CTableHeaderCell>
                <CTableHeaderCell>Transaction Date</CTableHeaderCell>
                <CTableHeaderCell>Tracking Time</CTableHeaderCell>
                <CTableHeaderCell>Notes</CTableHeaderCell>
                <CTableHeaderCell>Remarks</CTableHeaderCell>
                <CTableHeaderCell>Earnings Type</CTableHeaderCell>
                <CTableHeaderCell>Status</CTableHeaderCell>
                <CTableHeaderCell>Admin</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allEarnings?.map((item, index) => (
                  <CTableRow v-for="item in tableItems" className="mx-2" key={index}>
                     <CTableDataCell>
                      <CTooltip content={"Approve for confirmation"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow  px-2 pt-1 text-success m-1"
                          onClick={() => handleApproveEarning(item._id)}
                        >
                          <CIcon icon={cilThumbUp} />
                        </button>
                      </CTooltip>
                      <CTooltip content={"Approve for cancellation"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow m-1 px-2 pt-1 text-warning mx-1"
                          onClick={() => handleCancelEarning(item._id)}
                        >
                          <CIcon icon={cilThumbDown} />
                        </button>
                      </CTooltip>
                      <CTooltip content={"Edit Earnings"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow text-info px-2  m-1 pt-1"
                          onClick={(e) =>
                            navigate(`/earnings/edit/${item._id}`)
                          }
                        >
                          {" "}
                          <CIcon icon={cilPenAlt} />
                        </button>
                      </CTooltip>
                      <CTooltip content={"Delete Earnings"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow text-danger px-2  m-1 pt-1"
                          onClick={() => alertDelete(item._id)}
                        >
                          {" "}
                          <CIcon icon={cilTrash} />
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div className="">{item?.uid} </div>{" "}
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="">
                        {item?.user?.uid}/{item?.user?.email}{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.offer?.title}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.store?.name}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>
                        {item?.affiliation?.uid}/{item?.affiliation?.name}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.saleAmount}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.amountGot}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.amount}</div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div>{item?.orderCount}</div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{dateFormatter(item?.createdAt)}</div>
                    </CTableDataCell>
                    <CTableDataCell className="fs-sm">
                      <div>
                        <small>{dateFormatter(item?.createdAt)}</small>
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="text-center ">
                        {dateDeferenceFormatter(
                          item?.createdAt,
                          item?.trackingTime
                        )}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>
                        <ReadMore text={item?.notes} />{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>
                        <ReadMore text={item?.remarks} />{" "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="text-capitalize">
                        {item?.earningsType}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="text-capitalize">
                        {item?.status?.replace(/_/g, " ")}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="">{item?.createdBy?.name}</div>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete earnings ?"}
        setState={handleDeleteEarnings}
        setVisible={setVisible}
        visible={visible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default PendingEarnings;
