import { cilBar<PERSON><PERSON>, cilInfo, cilThumbDown, cilThumbUp } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeader<PERSON>ell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  cancelBulkEarning,
  cancelEarning,
  confirmEarning,
  fetchAllAffiliationsList,
  fetchAllStoresList,
  fetchAllTrackedEarnings,
} from "src/redux/features";
import { useNavigate } from "react-router-dom";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";
import SearchAndSelect from "src/components/select/SearchAndSelect";

function TrackedForConfirm() {
  const [visible, setVisible] = useState(false);
  const [deleteIds, setDeleteIds] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useState({
    genrealSearch: "",
    userIdSearch: "",
    orderIdSearch: "",
    advIdSearch: "",
    remarksSearch: "",
  });

  const [status, setStatus] = useState("all");
  const [store, setStore] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [affiliation, setAffiliation] = useState("");
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);

  const {
    page,
    pages,
    allTrackedEarnings,
    loading,

    trackedTotalAmountGotFromPartner: totalAmountGotFromPartner,
    trackedTotalAmountGotPending: totalAmountGotPending,
    trackedTotalAmountGotCancelled: totalAmountGotCancelled,
    trackedTotalAmountGotConfirmed: totalAmountGotConfirmed,
    trackedTotalAmountToGiveToUser: totalAmountToGiveToUser,
    trackedTotalAmountToGivePending: totalAmountToGivePending,
    trackedTotalAmountToGiveCancelled: totalAmountToGiveCancelled,
    trackedTotalAmountToGiveConfirmed: totalAmountToGiveConfirmed,
    trackedStatByStore: statByStore,
  } = useSelector((state) => state.earnings);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    //dispatch(fetchAllTrackedEarnings({ status: "tracked_for_confirm" }));
  }, [dispatch]);

  const handlePagination = async (value) => {
    const params = {
      search: searchParams.genrealSearch,
      status: "tracked_for_confirm",
      userId: searchParams.userIdSearch,
      orderId: searchParams.orderIdSearch,
      advId: searchParams.advIdSearch,
      remarks: searchParams.remarksSearch,
      startDate: startDate,
      endDate: endDate,
      store: store?._id,
      affiliation: affiliation?._id,
      page: value,
    };

    updateURLParams(params);
    dispatch(fetchAllTrackedEarnings(params));
  };

  const alertDelete = (earningId) => {
    setVisible(true);
    setDeleteIds(earningId);
  };
  const handleDelete = async () => {};

  const handleConfirmEarnings = async (earningIds) => {
    await dispatch(confirmEarning(earningIds)).unwrap();
    //dispatch(fetchAllAffiliationsList({}));
    //dispatch(fetchAllStoresList());
    handleURLParams();
    //dispatch(fetchAllTrackedEarnings({ status: "tracked_for_confirm" }));
  };

  const handleCancelEarnings = (earningId) => {
    dispatch(cancelBulkEarning(earningId));
    handleURLParams();
  };

  const handleSearchChange = (field) => (e) => {
    setSearchParams((prev) => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  const handleSelectStatus = (values) => {
    setStatus({
      _id: values?.value,
      label: values?.label,
    });
  };

  const handleSelectAffiliations = (values) => {
    setAffiliation({
      _id: values?.value,
      label: values?.label,
    });
  };

  const handleSelectStores = (values) => {
    setStore({
      _id: values?.value,
      label: values?.label,
    });
  };

  // Function to update URL parameters
  const updateURLParams = (params) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.set(key, value);
    });
    window.history.replaceState(null, "", `?${searchParams.toString()}`);
  };

  const handleFilter = async () => {
    const params = {
      search: searchParams.genrealSearch,
      status: "tracked_for_confirm",
      userId: searchParams.userIdSearch,
      orderId: searchParams.orderIdSearch,
      advId: searchParams.advIdSearch,
      remarks: searchParams.remarksSearch,
      startDate: startDate,
      endDate: endDate,
      store: store?._id,
      affiliation: affiliation?._id,
      page: 1,
    };

    updateURLParams(params);
    dispatch(fetchAllTrackedEarnings(params));
  };

  const handleReset = () => {
    setSearchParams({
      genrealSearch: "",
      userIdSearch: "",
      orderIdSearch: "",
      advIdSearch: "",
      remarksSearch: "",
    });
    setStartDate("");
    setEndDate("");
    setStore("");
    setAffiliation("");

    dispatch(
      fetchAllTrackedEarnings({ page: 1, status: "tracked_for_confirm" })
    );
    updateURLParams("");
  };

  const handleURLParams = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const params = Object.fromEntries(urlParams.entries());

    setSearchParams({
      genrealSearch: params.search,
      userIdSearch: params.userId,
      orderIdSearch: params.orderId,
      advIdSearch: params.advId,
      remarksSearch: params.remarks,
    });

    setStartDate(params.startDate);
    setEndDate(params.endDate);

    const storeItem = allStoresList.find((item) => item._id === params.store);
    const affiliationItem = allAffiliationsList.find(
      (item) => item._id === params.affiliation
    );

    setStore(
      storeItem
        ? {
            _id: storeItem._id,
            label: storeItem.name,
          }
        : ""
    );

    setAffiliation(
      affiliationItem
        ? {
            _id: affiliationItem._id,
            label:
              affiliationItem.name ||
              affiliationItem.title ||
              affiliationItem.sectionName,
          }
        : ""
    );

    dispatch(
      fetchAllTrackedEarnings({
        status: "tracked_for_confirm",
        affiliation: params.affiliation,
        store: params.store,
        search: params.search,
        userId: params.userId,
        orderId: params.orderId,
        advId: params.advId,
        remarks: params.remarks,
        startDate: params.startDate,
        endDate: params.endDate,
        page: params.page,
      })
    );
  };

  //write a useEffect to read url and set the all params
  useEffect(() => {
    if (allStoresList.length > 0 && allAffiliationsList.length > 0) {
      handleURLParams();
    }
  }, [allStoresList, allAffiliationsList]);

  return (
    <CContainer fluid>
      <CRow className="border rounded p-2 mt-10 pt-[10px]">
        <CCol md={4} xl={3}>
          <label htmlFor="">Select Partners</label>
          <SearchAndSelect
            array={allAffiliationsList}
            handleSelectedValue={handleSelectAffiliations}
            placeholder={"Select Partners..."}
            defaultValue={affiliation}
          />
        </CCol>

        <CCol md={4} xl={3}>
          <label htmlFor="">Select Stores</label>
          <SearchAndSelect
            array={allStoresList}
            handleSelectedValue={handleSelectStores}
            placeholder={"Select Stores..."}
            defaultValue={store}
          />
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">
            Store Name / User Name / Email / Earning Id :
          </label>
          <div className="">
            <input
              type="text"
              onChange={handleSearchChange("genrealSearch")}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.genrealSearch}
            />
          </div>
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">User Id</label>
          <div className="">
            <input
              type="text"
              value={searchParams.userIdSearch}
              onChange={handleSearchChange("userIdSearch")}
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
            />
          </div>
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">Order Unique Id</label>
          <div className="">
            <input
              type="text"
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.orderIdSearch}
              onChange={handleSearchChange("orderIdSearch")}
            />
          </div>
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">Adv Id</label>
          <div className="">
            <input
              type="text"
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.advIdSearch}
              onChange={handleSearchChange("advIdSearch")}
            />
          </div>
        </CCol>

        <CCol sm={12} xl={6}>
          <label htmlFor="">Remarks</label>
          <div className="">
            <input
              type="text"
              className="my-2 p-1 px-2 rounded border w-75"
              placeholder="Search..."
              value={searchParams.remarksSearch}
              onChange={handleSearchChange("remarksSearch")}
            />
          </div>
        </CCol>
        <CRow className="mt-3">
          <CCol xs={12} className="mb-2">
            <p className="h6"> Date Range :</p>
            <div className="d-flex">
              <div>
                <label htmlFor="">Start Date</label>
                <input
                  type="date"
                  name=""
                  className="border rounded  p-2 m-2"
                  onChange={(e) => setStartDate(e.target.value)}
                  placeholder=""
                  id=""
                  value={startDate}
                />
              </div>
              <div>
                <label htmlFor="">End Date</label>
                <input
                  type="date"
                  name=""
                  className="border rounded  p-2 m-2"
                  onChange={(e) => setEndDate(e.target.value)}
                  id=""
                  value={endDate}
                />
              </div>
            </div>
          </CCol>
        </CRow>

        <CCol className="mt-3 d-flex" sm={12}>
          <button
            onClick={
              () => handleReset()
              // setRefresh(!refresh)
            }
            type="reset"
            className="btn-danger btn text-light px-5 py-2 me-auto mx-2 btn-sm"
          >
            reset
          </button>
          <button
            onClick={() => handleFilter()}
            type="submit"
            className="btn-primary btn px-5 py-2 mx-2 ms-auto btn-sm "
          >
            filter
          </button>
        </CCol>
      </CRow>
      {(searchParams.genrelSearch ||
        searchParams.userIdSearch ||
        searchParams.orderIdSearch ||
        searchParams.advIdSearch ||
        searchParams.remarksSearch ||
        startDate ||
        endDate ||
        (status && status._id !== "all") ||
        store ||
        affiliation) &&
        statByStore.length > 0 && (
          <div>
            <CRow className="my-2 ">
              <CCol
                className="mt-3 d-flex rounded-lg justify-content-around"
                sm={12}
                xs={12}
              >
                <div className="w-full">
                  {/* <CRow className="my-2">
                    <div className="fw-bold">
                      Total amount got from partner: {totalAmountGotFromPartner}{" "}
                    </div>
                  </CRow> */}
                  {/* <CRow className="my-2">
                    <div className="fw-bold">
                      Pending amount from partner: {totalAmountGotPending}{" "}
                    </div>
                  </CRow> */}
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Confirmed amount from partner: {totalAmountGotConfirmed}{" "}
                    </div>
                  </CRow>
                  {/* <CRow className="my-2">
                    <div className="fw-bold">
                      Cancelled amount from partner: {totalAmountGotCancelled}{" "}
                    </div>
                  </CRow> */}
                </div>

                <div className="w-full">
                  {/* <CRow className="my-2">
                    <div className="fw-bold">
                      Total amount to give to users: {totalAmountToGiveToUser}{" "}
                    </div>
                  </CRow> */}
                  {/* <CRow className="my-2">
                    <div className="fw-bold">
                      Pending to give to users: {totalAmountToGivePending}{" "}
                    </div>
                  </CRow> */}
                  {/* <CRow className="my-2">
                    <div className="fw-bold">
                      Cancelled to give to users: {totalAmountToGiveCancelled}{" "}
                    </div>
                  </CRow> */}
                  <CRow className="my-2">
                    <div className="fw-bold">
                      Confirmed to give to users: {totalAmountToGiveConfirmed}{" "}
                    </div>
                  </CRow>
                </div>
              </CCol>
            </CRow>

            <CRow className="">
              <CTable align="middle" className=" border" striped hover bordered>
                <CTableHead color="dark">
                  <CTableRow>
                    <CTableHeaderCell>Store</CTableHeaderCell>
                    <CTableHeaderCell>Count</CTableHeaderCell>
                    <CTableHeaderCell>Amount Got</CTableHeaderCell>
                    <CTableHeaderCell>Amount To Give</CTableHeaderCell>
                    <CTableHeaderCell>Sale Amount</CTableHeaderCell>
                    <CTableHeaderCell>AOV</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {!loading &&
                    statByStore?.map((item) => (
                      <CTableRow
                        v-for="item in tableItems"
                        className=" "
                        //color={!item.active ? "danger" : ""}
                        key={item._id}
                      >
                        <CTableDataCell>
                          <div className="text-center">{item?.storeName}</div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">{item?.count}</div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">
                            {item?.totalAmountGot.toFixed(2)}
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">
                            {item?.totalAmountToGive.toFixed(2)}
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <div className="text-center">
                            {item?.totalSaleAmount.toFixed(2)}
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>{item?.aov.toFixed(2)}</CTableDataCell>
                      </CTableRow>
                    ))}
                </CTableBody>
              </CTable>

              {loading && <LoadingComponent />}
            </CRow>
          </div>
        )}

      {/* <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      /> */}
      <CRow>
        <CCol className="bg-white border py-3 rounded" md={12}>
          <CTable
            align="middle"
            className=" border "
            striped
            hover
            bordered
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="">ID</CTableHeaderCell>
                <CTableHeaderCell>User Name</CTableHeaderCell>
                <CTableHeaderCell>User Id</CTableHeaderCell>
                <CTableHeaderCell>Store And Earnings Details</CTableHeaderCell>
                <CTableHeaderCell>actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allTrackedEarnings?.map((item, index) => (
                  <CTableRow v-for="item in tableItems" key={index}>
                    <CTableDataCell className="text-center">
                      <div className="">{index} </div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div className="">
                        {item?.user?.name}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell className="text-center">
                      <div className="">{item.user.uid} </div>
                    </CTableDataCell>
                    <CTable
                      align="middle"
                      className=" border "
                      striped
                      hover
                      bordered
                      responsive
                    >
                      <CTableHead color="dark">
                        <CTableRow>
                          <CTableHeaderCell>storeId</CTableHeaderCell>
                          <CTableHeaderCell>Store Name </CTableHeaderCell>
                          <CTableHeaderCell>Total SaleAmount</CTableHeaderCell>
                          <CTableHeaderCell>Total AmountGot</CTableHeaderCell>
                          <CTableHeaderCell>
                            Total CashbackAmount
                          </CTableHeaderCell>
                          <CTableHeaderCell>
                            Total Earnings count
                          </CTableHeaderCell>
                          <CTableHeaderCell>Status</CTableHeaderCell>
                          <CTableHeaderCell>Actions</CTableHeaderCell>
                        </CTableRow>
                      </CTableHead>
                      <CTableBody>
                        {item?.stores?.map((storeItem) => (
                          <CTableRow
                            className="border "
                            v-for="item in tableItems "
                            key={storeItem.store}
                          >
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem.storeId}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem.storeName}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.saleAmount}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.amountGot}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.cashbackAmount}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.earningsCount}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell>
                              <div className="text-center">
                                {storeItem?.earningsDetails?.status}{" "}
                              </div>
                            </CTableDataCell>
                            <CTableDataCell className=" d-flex">
                              <CTooltip content={"Approve All Orders"}>
                                <button
                                  type="button"
                                  style={{ height: "2rem", fontSize: "12px" }}
                                  className={`border rounded shadow  text-success px-2 pt-1 m-1 `}
                                  onClick={() =>
                                    handleConfirmEarnings(
                                      storeItem?.earningsDetails?.earningIds
                                    )
                                  }
                                  // disabled={
                                  //   storeItem?.earningsDetails
                                  //     ?.earningsCount !== 1
                                  // }
                                >
                                  <CIcon icon={cilThumbUp} />
                                </button>
                              </CTooltip>
                              <CTooltip content={"Reject All Orders"}>
                                <button
                                  type="button"
                                  style={{ height: "2rem", fontSize: "12px" }}
                                  className={`border rounded shadow  text-danger px-2 pt-1 m-1 `}
                                  onClick={() =>
                                    handleCancelEarnings(
                                      storeItem?.earningsDetails?.earningIds
                                    )
                                  }
                                  //disabled={
                                  //  storeItem?.earningsDetails
                                  //    ?.earningsCount !== 1
                                  //}
                                >
                                  <CIcon icon={cilThumbDown} />
                                </button>
                              </CTooltip>
                              <CTooltip content={"view individual orders"}>
                                <button
                                  type="button"
                                  style={{ height: "2rem", fontSize: "12px" }}
                                  className={`border rounded shadow  text-primary px-2 pt-1 m-1 `}
                                  onClick={() => {
                                    const url = `/earnings?userId=${item?.user?.uid}&status=tracked_for_confirm&store=${storeItem?.store}&page=1`;
                                    window.open(url, "_blank");
                                  }}
                                >
                                  <CIcon icon={cilInfo} />
                                </button>
                              </CTooltip>
                            </CTableDataCell>
                          </CTableRow>
                        ))}{" "}
                      </CTableBody>
                    </CTable>
                    <CTableDataCell className="text-center">
                      <CTooltip content={"user statistics"}>
                        <button
                          type="button"
                          style={{ height: "2rem", fontSize: "12px" }}
                          className="border rounded shadow text-info px-2  m-1 pt-1"
                        >
                          <CIcon icon={cilBarChart} />
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete ?"}
        setState={handleDelete}
        setVisible={setVisible}
        visible={visible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default TrackedForConfirm;
