.form-multi-select-all {
    border: none !important;
    margin-left: rem;
    width: 100%;
    padding-top: .5rem;
    padding-bottom: .5rem;
}

.dropdown-menu {
    width: 97%;
}

button {
    border: none;
    outline: none;
}

.form-multi-select-tag-delete {
    border-radius: 5px;
    background: none;
    padding: 5px;
}

.form-multi-select-tag {
    border: 1px solid #b1b7c1b1;
    padding: .2rem;
    background: #bfc2c747;
    border-radius: 5px;
    margin-left: 5px;
    margin-bottom: 5px;
    margin-top: 5px;
}

.form-multi-select-search {
    min-width: 20rem;
    border: 1px solid #b1b7c1;
    border-radius: 5px;
    padding: .4rem;
    outline: none;
}

.form-multi-select-option {
    margin-right: .5rem;
    padding: 10px;
}

.form-multi-select-optgroup-label {
    font-weight: 600;
    background: #e4e4e465;
    padding: .5rem;
    
    text-transform: capitalize;
}
.rotate{
    transition: all 0.4s ease;
    transform: rotate(180deg);
}

.reverse{
    transition: all 0.4s ease;
    transform: rotate(0deg);
}