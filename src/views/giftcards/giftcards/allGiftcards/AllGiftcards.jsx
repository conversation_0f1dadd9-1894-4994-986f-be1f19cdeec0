import { cilArrow<PERSON>ottom, cilPen, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  <PERSON><PERSON>,
  CContainer,
  CFormCheck,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import "../../giftcard.css";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteGiftCard,
  fetchAllGiftCards,
  handleOpenModal,
  updateGiftCardPriority,
} from "src/redux/features";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AllGiftCards() {
  const [status, setStatus] = useState(2);
  const [priority, setPriority] = useState(-1);
  const [search, setSearch] = useState("");
  const [name, setName] = useState(-1);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { page, pages, pageSize, allGiftCards, loading } = useSelector(
    (state) => state.giftCard
  );

  useEffect(() => {
    dispatch(fetchAllGiftCards({ name, priority, search, status }));
  }, [dispatch, name, priority, search, status]);

  const handleClickPriority = async (giftCardId, priority) => {
    dispatch(updateGiftCardPriority({ giftCardId, priority }));
  };

  const handleDelete = async (id) => {
    dispatch(deleteGiftCard(id));
  };

  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllGiftCards({ search: value }));
  };

  const handlePagination = async (value) => {
    dispatch(fetchAllGiftCards({ page: value }));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="">
        <CCol className=" " sm={3}>
          <input
            type="text"
            onChange={(e) => handleSearch(e.target.value)}
            className="my-2 p-1 px-2 w-100 rounded border"
            placeholder="Search..."
          />
        </CCol>
      </CRow>
      <CRow>
        <CCol sm={12}>
          <p className="h6">Status :</p>
        </CCol>
        <CCol sm={3}>
          <CFormCheck
            type="radio"
            name="exampleRadio"
            id="exampleRadio1"
            value="0"
            onClick={() => setStatus(0)}
            label="show only active giftcards"
          />
        </CCol>
        <CCol sm={3}>
          <CFormCheck
            type="radio"
            name="exampleRadio"
            id="exampleRadio2"
            value="1"
            onClick={() => setStatus(1)}
            label="show only inactive giftcards"
          />
        </CCol>
        <CCol sm={3}>
          <CFormCheck
            type="radio"
            name="exampleRadio"
            id="exampleRadio3"
            value="2"
            onClick={() => setStatus(2)}
            label="Show All giftcards"
            defaultChecked
          />
        </CCol>
      </CRow>
      <CRow>
        <CCol className="ms-auto text-end" sm={2}>
          <button
            type="button"
            onClick={() => navigate("/giftcards/create-giftcard")}
            className=" my-2 btn btn-primary btn-sm mx-auto"
          >
            Create Giftcard
          </button>
        </CCol>
      </CRow>
      {/* <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={3}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-end  ms-auto   " sm={8}>
          <button
            type="button"
            className={` ${
              name === 1  ? " border-bottom border-primary border-3 bg-none" : " "
            }  border-0  mx-2 `}
            onClick={() => setName(name == 1 ? 0 : 1)}
          >
            Name{" "}
            <CIcon
              className={`${name === 1 ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              priority ==1 ? "border-bottom border-primary border-3 bg-none" : ""
            }  border-0 mx-2  `}
            onClick={() => setPriority(1)}
          >
            Highest Priority{" "}
            <CIcon
              className={`${priority == 1 ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            type="button"
            className={`${
              priority ==0
                ? "border-bottom border-primary border-3 bg-none"
                : " "
            } border-0  mx-2  `}
            onClick={() => setPriority(0)}
          >
            Lowest Priority{" "}
            <CIcon
              className={`${priority ==0 ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow> */}
      <CTable
        align="middle"
        className="mb-0 border "
        hover
        striped
        bordered
        borderColor="secondary"
      >
        <CTableHead color="dark">
          <CTableRow>
            <CTableHeaderCell className="text-center"> UID </CTableHeaderCell>
            <CTableHeaderCell> Name</CTableHeaderCell>
            <CTableHeaderCell className="">Logo</CTableHeaderCell>
            <CTableHeaderCell className="">Email Image</CTableHeaderCell>
            <CTableHeaderCell>Provider</CTableHeaderCell>
            <CTableHeaderCell>QC ID</CTableHeaderCell>
            <CTableHeaderCell> Priority </CTableHeaderCell>
            <CTableHeaderCell>Related CB Store</CTableHeaderCell>
            <CTableHeaderCell>Discount Getting</CTableHeaderCell>
            <CTableHeaderCell>Cashback Giving</CTableHeaderCell>
            <CTableHeaderCell>Changed By</CTableHeaderCell>
            <CTableHeaderCell>Added Date</CTableHeaderCell>
            <CTableHeaderCell>Actions</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {!loading &&
            allGiftCards?.map((item) => (
              <CTableRow color={!item.active && "danger"} key={item._id}>
                <CTableDataCell>{item?.uid}</CTableDataCell>
                <CTableDataCell>
                  <div>{item.name}</div>
                </CTableDataCell>
                <CTableDataCell className="">
                  {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                  <img
                    className=""
                    style={{ cursor: "pointer" }}
                    onClick={() =>
                      dispatch(
                        handleOpenModal({ image: item?.logo?.secureUrl })
                      )
                    }
                    width={50}
                    src={item?.logo?.secureUrl}
                    alt=""
                  />
                </CTableDataCell>
                <CTableDataCell className="">
                  {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                  <img
                    className=""
                    style={{ cursor: "pointer" }}
                    onClick={() =>
                      dispatch(
                        handleOpenModal({ image: item?.image?.secureUrl })
                      )
                    }
                    width={50}
                    src={item?.image?.secureUrl}
                    alt=""
                  />
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center"> {item.provider}</div>
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center">{item.qwickcilverId}</div>
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center">
                    <input
                      // type="number"
                      type="decimal"
                      className="w-100 border rounded p-2"
                      value={item?.priority}
                      onChange={(e) =>
                        handleClickPriority(item._id, e.target.value)
                      }
                    />
                  </div>
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center">
                    {" "}
                    {item?.relatedCbStore?.name}
                  </div>
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center"> {item.discountGetting}</div>
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center">{item.cashbackGiving}</div>
                </CTableDataCell>
                <CTableDataCell>
                  <div>{item?.updatedBy?.name}</div>
                </CTableDataCell>
                <CTableDataCell className="">
                  <div className="text-center">
                    {" "}
                    {dateFormatter(item.createdAt)}
                  </div>
                </CTableDataCell>
                <CTableDataCell className="">
                  <CTableDataCell className="text-center ">
                    <CTooltip content="edit giftcard">
                      <button
                        type="button"
                        onClick={() =>
                          navigate(`/giftcards/update-giftcard/${item._id}`)
                        }
                        className="border rounded shadow px-2 py-1 text-primary m-1"
                      >
                        <CIcon icon={cilPen} />
                      </button>
                    </CTooltip>
                    <CTooltip content="delete giftcard">
                      <button
                        type="button"
                        onClick={() => handleDelete(item._id)}
                        className={`${
                          item.disable ? " text-secondary" : "text-danger"
                        } border rounded shadow px-2 py-1 m-1 `}
                      >
                        {" "}
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableDataCell>
              </CTableRow>
            ))}
        </CTableBody>
      </CTable>
      {loading && <LoadingComponent />}
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllGiftCards;
