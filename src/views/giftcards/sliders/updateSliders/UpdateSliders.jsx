import { <PERSON><PERSON>, <PERSON>ontainer, CForm, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";
import CIcon from "@coreui/icons-react";
import { cilCloudUpload, cilTrash } from "@coreui/icons";
import ImageUploader from "src/components/fileManagers/imageUploader";
import { useDispatch, useSelector } from "react-redux";
import { fetchGiftCardSliderDetails, updateGiftCardSlider } from "src/redux/features";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";
import { useParams } from "react-router-dom";
import { dateInDDMMYYYYFormat } from "src/helperFunctions/dateFormatter";

const currentDateTime = new Date().toISOString().slice(0, 16);
function UpdateGiftCardSlider() {
  const [expiryDate, setExpiryDate] = useState("");
  const [redirectUrl, setRedirectUrl] = useState("");
  const [termsContent, setTermsContent] = useState("");
  const [termsTitle, setTermsTitle] = useState("");
  const [desktopBanner, setDesktopBanner] = useState(null);
  const [mobileBanner, setMobileBanner] = useState(null);
  const dispatch = useDispatch();

  const { sliderId } = useParams()
  const { giftCardSliderDetails } = useSelector((state) => state.giftCardSlider)
  useEffect(() => {
    if (sliderId) {
      dispatch(fetchGiftCardSliderDetails(sliderId))
    }

  }, [sliderId, dispatch])

  useEffect(() => {
    if (giftCardSliderDetails) {
      setDesktopBanner(giftCardSliderDetails?.desktopBanner)
      setMobileBanner(giftCardSliderDetails?.mobileBanner)
      setExpiryDate(giftCardSliderDetails?.expiryDate)
      setRedirectUrl(giftCardSliderDetails?.redirectUrl)
      setTermsContent(giftCardSliderDetails?.termsContent)
      setTermsTitle(giftCardSliderDetails?.termsTitle)
    }

  }, [giftCardSliderDetails])



  const handleSubmit = (e) => {
    e.preventDefault();
    if (!desktopBanner) {
      return toast.custom(<Danger message={"please update desktop banner"} />)
    }
    if (!mobileBanner) {
      return toast.custom(<Danger message={"please update mobile banner"} />)
    }
    const payload = {
      expiryDate,
      redirectUrl,
      mobileBanner,
      desktopBanner,
      type: 'giftCard',
      termsContent,
      termsTitle
    }

    dispatch(updateGiftCardSlider({ sliderId, payload }));
  };
  return (
    <CContainer fluid>
      <CRow className="">
        <div className=" mb-2 ">
          <h4 className="text-secondary text-center ">Update Giftcard Slider</h4>
        </div>
        <CForm className="shadow rounded mx-auto " onSubmit={handleSubmit}  >
          <CRow>
            <CCol className="   " md={6} >
              <p className="text-center h6 my-2">Desktop Banner</p>
              <div
                style={{ height: "15rem" }}
                className={` ${desktopBanner ? " " : "offer-frame "
                  } my-2 w-100 p-0  hover d-flex justify-content-center align-items-center shadow`}
              >
                <div className="w-100 mx-auto text-center ">
                  {desktopBanner && (
                    <img
                      style={{ maxHeight: "15rem" }}
                      className="w-100 offer-img my-2"
                      src={desktopBanner?.secureUrl}
                      alt=""
                    />
                  )}
                </div>
              </div>
              <div className="d-flex justify-content-center align-items-center my-2">
                <ImageUploader
                  setFile={setDesktopBanner}
                  icon={cilCloudUpload}
                  keyword={"Upload Image"}
                  accept={".webp,.jpg,.jpeg,.png"}
                  label={"desktop_image"}
                />
                <button
                  type="button"
                  className="btn btn-outline-danger mx-2 "
                  onClick={() => setDesktopBanner(null)}
                >
                  <CIcon icon={cilTrash} />
                  Remove
                </button>
              </div>

            </CCol>
            <CCol className="  " md={6} >
              <p className="text-center h6 my-2">Mobile Banner</p>
              <div
                style={{ height: "15rem" }}
                className={` ${mobileBanner ? " " : "offer-frame "
                  } my-2 w-100 p-0  hover d-flex justify-content-center align-items-center shadow `}
              >
                <div className="w-100 mx-auto text-center ">
                  {mobileBanner && (
                    <img
                      style={{ maxHeight: "15rem" }}
                      className="w-100 offer-img my-2"
                      src={mobileBanner?.secureUrl}
                      alt=""
                    />
                  )}
                </div>

              </div>
              <div className="d-flex justify-content-center align-items-center my-2">
                <ImageUploader
                  setFile={setMobileBanner}
                  icon={cilCloudUpload}
                  keyword={"Upload Image"}
                  accept={".webp,.jpg,.jpeg,.png"}
                  label={"mobile_image"}
                />
                <button
                  type="button"
                  className="btn btn-outline-danger mx-2 "
                  onClick={() => setMobileBanner(null)}
                >
                  <CIcon icon={cilTrash} />
                  Remove
                </button>
              </div>
            </CCol>
          </CRow>
          <div className="my-3 ">
            <label className="w-100 my-2" htmlFor="">
              Terms Title
            </label>
            <textarea
              name=""
              id=""
              rows="3"
              required
              value={termsTitle}
              placeholder="Terms Title"
              className=" w-50 border rounded"
              onChange={(e) => setTermsTitle(e.target.value)}
            />
          </div>
          <div className="my-3 ">
            <label className="w-100 my-2" htmlFor="">
              Terms Content
            </label>
            <textarea
              name=""
              id=""
              rows="3"
              required
              value={termsContent}
              placeholder="Terms Content"
              className=" w-50 border rounded"
              onChange={(e) => setTermsContent(e.target.value)}
            />
          </div>
          <div className="my-3 ">
            <label className="w-100 my-2" htmlFor="">
              Redirect Url
            </label>
            <textarea
              name=""
              id=""
              rows="3"
              required
              value={redirectUrl}
              placeholder="Redirect url"
              className=" w-50 border rounded"
              onChange={(e) => setRedirectUrl(e.target.value)}
            />
          </div>
          <div className="my-3">
            <label className="w-100 my-2" htmlFor="">
              Expiry Date
            </label>
            <input
              className="w-50 p-2 rounded border "
              type="datetime-local"
              id="datetime"
              name="datetime"
              value={dateInDDMMYYYYFormat(expiryDate)}
              onChange={(e) => setExpiryDate(e.target.value)}
              min={currentDateTime}
              required
            />
          </div>

          <div className="d-flex justify-content-center align-items-center my-4">
            <button className="btn btn-outline-danger mx-2" type="reset">
              Cancel
            </button>
            <button className="btn btn-outline-success mx-2" type="submit">
              Update
            </button>
          </div>
        </CForm>
      </CRow>
    </CContainer>
  );
}


export default UpdateGiftCardSlider;
