import {
  <PERSON><PERSON>,
  CContainer,
  CRow,
  CTable,
  CTable<PERSON><PERSON>,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import {  fetchAllIcbGiftCardOrders } from "src/redux/features";

function AllIcbGiftCardOrders() {
  const [search, setSearch] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { page, pages, pagesize, allIcbGiftCardOrde<PERSON>, loading } = useSelector(
    (state) => state.icbGiftCardOrder
  );

  useEffect(() => {
    dispatch(fetchAllIcbGiftCardOrders({}));
  }, [dispatch]);

  const handleSearch = async (value) => {
    dispatch(fetchAllIcbGiftCardOrders({ search: value }));
  };

  const handleFilter = async (event) => {};

  const handlePagination = async (value) => {
    dispatch(fetchAllIcbGiftCardOrders({ page: value }));
  };

  const handleSelectOrderStatus = (values) => {
    dispatch(fetchAllIcbGiftCardOrders({ status: values?.value }));
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow >
        <CCol md={4} sm={7} lg={3} >
        <input
          type="text"
          className="w-100 border rounded py-1 px-2 "
          placeholder="Search...."
          value={search}
          onChange={(e) => handleSearch(e.target.value)}
        />
        </CCol>
      </CRow>
      <CRow className="py-3">
        <CCol sm={4}>
          <SearchAndSelect
            array={[
              { name: "Un Paid", _id: "unpaid" },
              { name: "Failed", _id: "failed" },
              { name: "Success", _id: "success" },
            ]}
            handleSelectedValue={handleSelectOrderStatus}
            placeholder={"Select Order Status..."}
          />
        </CCol>
        <CCol sm={6}>
          <label htmlFor=""> Start Date :</label>
          <input
            type="date"
            className="p-2 border rounded mx-2"
            name=""
            id=""
            onChange={(e) => setFromDate(e.target.value)}
          />
          <label htmlFor=""> End Date :</label>
          <input
            type="date"
            className="p-2 border rounded mx-2"
            name=""
            id=""
            onChange={(e) => setToDate(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow>
      <CTable
       align="middle"
       className="m-0 border"
       hover
       striped
       bordered
       borderColor="secondary"
      >
        <CTableHead color="dark">
          <CTableRow>
            <CTableHeaderCell className="text-center"> No </CTableHeaderCell>
            <CTableHeaderCell> Name</CTableHeaderCell>
            <CTableHeaderCell className="">
              order Email/Name/Mobile
            </CTableHeaderCell>
            <CTableHeaderCell className="">Store</CTableHeaderCell>
            <CTableHeaderCell className="text-center">Amount</CTableHeaderCell>
            <CTableHeaderCell>Discounted Amount</CTableHeaderCell>
            <CTableHeaderCell>ICB Bal Used</CTableHeaderCell>
            <CTableHeaderCell>Pay U</CTableHeaderCell>
            <CTableHeaderCell>Cash free</CTableHeaderCell>
            <CTableHeaderCell>Paypal</CTableHeaderCell>
            <CTableHeaderCell>Razor</CTableHeaderCell>
            <CTableHeaderCell>Status</CTableHeaderCell>
            <CTableHeaderCell>QC Balance</CTableHeaderCell>
            <CTableHeaderCell>Msg</CTableHeaderCell>
            <CTableHeaderCell>User Note</CTableHeaderCell>
            <CTableHeaderCell>IP</CTableHeaderCell>
            <CTableHeaderCell>Device</CTableHeaderCell>
            <CTableHeaderCell>Transaction Time</CTableHeaderCell>
            <CTableHeaderCell>Completion Time</CTableHeaderCell>
            <CTableHeaderCell>Redeem User</CTableHeaderCell>
            <CTableHeaderCell>Comments</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {!loading &&
            allIcbGiftCardOrders?.map((item, index) => (
              <CTableRow v-for="item in tableItems" key={item._id}>
                <CTableDataCell className="text-center">
                  {" "}
                  {item?.uid}{" "}
                </CTableDataCell>
                <CTableDataCell>
                  {" "}
                  <div className="text-center">{item.name}</div>{" "}
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center"> {item?.mobile} </div>{" "}
                </CTableDataCell>
                <CTableDataCell>
                  <div className="text-center"> {item?.store?.name}</div>{" "}
                </CTableDataCell>
                <CTableDataCell>
                  {" "}
                  <div className="text-center">{item?.amount}</div>{" "}
                </CTableDataCell>
                <CTableDataCell className="">
                  {" "}
                  <div className="text-center"> </div>{" "}
                </CTableDataCell>
                <CTableDataCell className="">
                  {" "}
                  <div className="text-center"> </div>{" "}
                </CTableDataCell>
                <CTableDataCell className=""></CTableDataCell>
                <CTableDataCell className=""></CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className="">
                  <div className="text-center">
                    {item.status === 0 && "unpaid"}
                    {item.status === 1 && "failed"}
                    {item.status === 2 && "success"}{" "}
                  </div>{" "}
                </CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className="">
                  {" "}
                  <div className="text-center">{item.userIp}</div>{" "}
                </CTableDataCell>
                <CTableDataCell className="">
                  {" "}
                  <div className="text-center">{item.device}</div>{" "}
                </CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className=""> </CTableDataCell>
                <CTableDataCell className="text-center "> </CTableDataCell>
              </CTableRow>
            ))}
        </CTableBody>
      </CTable>
      </CRow>
      {loading && <LoadingComponent />}
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}


export default AllIcbGiftCardOrders;
