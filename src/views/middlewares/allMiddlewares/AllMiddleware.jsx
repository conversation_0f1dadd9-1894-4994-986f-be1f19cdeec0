import React, { useEffect, useState } from "react";
import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilPeople,
  cilXCircle,
  cilCheckCircle,
  cilPencil,
  cilLockLocked,
  cilLockUnlocked,
  cilSave,
} from "@coreui/icons";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  addMiddlewareAction,
  fetchAllMiddlewares,
  fetchAllServerFunctions,
  removeMiddlewareAction,
  updateMiddleware,
} from "src/redux/features";

const Middlewares = () => {
  const [activeInp, setActiveInp] = useState({
    active: false,
    id: "",
    name: "",
  });
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allMiddlewares, allFunctions } = useSelector(
    (state) => state.middleware
  );
  useEffect(() => {
    dispatch(fetchAllMiddlewares({}));
    dispatch(fetchAllServerFunctions({}));
  }, [dispatch]);

  const handleGiveAccess = async (features, middlewareId) => {
    dispatch(addMiddlewareAction({ features, middlewareId }));
  };

  const handleRemoveAccess = async (features, middlewareId) => {
    dispatch(removeMiddlewareAction({ features, middlewareId }));
  };
  const handleUpdateName = async (name, middlewareId) => {
    dispatch(updateMiddleware({ middlewareId, name }));
  };
  return (
    <CContainer fluid>
      <div className="mb-3 text-end ">
        <CButton
          onClick={() => navigate("/access-controls/create-access-controls")}
          color="primary"
          className=""
        >
          Create New Role{" "}
        </CButton>
      </div>
      <CRow>
        <CCol className="" sm={8} xs={12} md={5}>
          <CCard className=" my-4 ">
            <CCardBody className="p-4">
              <h6>All Rolls</h6>
              <CTable
                align="middle"
                className="mb-0 border"
                hover
                striped
                bordered
                borderColor="secondary"
                responsive
              >
                <CTableHead color="dark">
                  <CTableRow>
                    <CTableHeaderCell className="text-center">
                      <CIcon icon={cilPeople} />
                    </CTableHeaderCell>
                    <CTableHeaderCell className="text-center">
                      roles
                    </CTableHeaderCell>
                    <CTableHeaderCell className="text-center">
                      {" "}
                      Levels
                    </CTableHeaderCell>
                    <CTableHeaderCell className="text-center">
                      {" "}
                      Actions
                    </CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {allMiddlewares?.map((item) => (
                    <CTableRow key={item._id}>
                      <CTableDataCell>{item?.uid}</CTableDataCell>
                      <CTableDataCell>
                        <input
                          type="text"
                          value={activeInp?.name || item?.name}
                          onChange={(e) =>
                            handleUpdateName(e.target.value, item._id)
                          }
                          disabled={activeInp.id !== item._id}
                          className={
                            activeInp?.id === item._id
                              ? "border rounded p-2 "
                              : " p-2 border-0 bg-transparent "
                          }
                        />
                      </CTableDataCell>
                      <CTableDataCell className="text-center">
                        {item?.level}
                      </CTableDataCell>
                      <CTableDataCell>
                        <CTooltip content={"edit role name"}>
                          {activeInp?.id === item._id ? (
                            <button
                              type="button"
                              className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                              onClick={() =>
                                setActiveInp({ active: false, id: "" })
                              }
                            >
                              <CIcon icon={cilSave} />
                            </button>
                          ) : (
                            <button
                              type="button"
                              className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                              onClick={() =>
                                setActiveInp({ active: true, id: item?._id })
                              }
                            >
                              <CIcon icon={cilPencil} />
                            </button>
                          )}
                        </CTooltip>
                        <CTooltip content={item.block ? "unblock " : "block "}>
                          <button
                            type="button"
                            className={`${item.block ? "text-danger " : " text-success"
                              } border rounded shadow px-2 py-1 mx-1`}
                            onClick={() => handleBlock(item._id)}
                          >
                            {item.block ? (
                              <CIcon icon={cilLockLocked} />
                            ) : (
                              <CIcon icon={cilLockUnlocked} />
                            )}
                          </button>
                        </CTooltip>
                      </CTableDataCell>
                    </CTableRow>
                  ))}
                </CTableBody>
              </CTable>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CTable
            align="middle"
            className="mb-0 border"
            hover
            striped
            bordered
            borderColor="secondary"
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="text-center">
                  <CIcon icon={cilPeople} />
                </CTableHeaderCell>
                <CTableHeaderCell>actions</CTableHeaderCell>
                {allMiddlewares?.map((item) => (
                  <CTableHeaderCell key={item?._id}>{item.name} </CTableHeaderCell>
                ))}
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {allFunctions?.map((item, index) => (
                <CTableRow v-for="item in tableItems" key={item?._id}>
                  <CTableDataCell className="text-center">
                    <div>{index + 1}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.replace(/_/g, " ")}</div>
                  </CTableDataCell>
                  {allMiddlewares?.length !== 0 &&
                    allMiddlewares?.map((data, inx) => {
                      if (index === inx) {
                        if (data.features.includes(item)) {
                          return (
                            <CTableDataCell key={data?._id}>
                              <CTooltip content="remove access">
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleRemoveAccess(item, data.level)
                                  }
                                  className="border rounded px-2 shadow py-1"
                                >
                                  <CIcon
                                    className=" text-success "
                                    color="success"
                                    icon={cilCheckCircle}
                                  />
                                </button>
                              </CTooltip>
                            </CTableDataCell>
                          );
                        } else {
                          return (
                            <CTableDataCell key={item?._id}>
                              <CTooltip content="give access">
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleGiveAccess(item, data.level)
                                  }
                                  className="border rounded px-2 shadow py-1"
                                >
                                  <CIcon
                                    className=" text-danger "
                                    color="danger"
                                    icon={cilXCircle}
                                  />
                                </button>
                              </CTooltip>
                            </CTableDataCell>
                          );
                        }
                      } else {
                        if (data.features.includes(item)) {
                          return (
                            <CTableDataCell key={item?._id}>
                              <CTooltip content="remove access">
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleRemoveAccess(item, data.level)
                                  }
                                  className="border rounded px-2 shadow py-1"
                                >
                                  <CIcon
                                    className=" text-success "
                                    color="success"
                                    icon={cilCheckCircle}
                                  />
                                </button>
                              </CTooltip>
                            </CTableDataCell>
                          );
                        } else {
                          return (
                            <CTableDataCell key={item?._id}>
                              <CTooltip content="give access">
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleGiveAccess(item, data.level)
                                  }
                                  className="border rounded px-2 shadow py-1"
                                >
                                  <CIcon
                                    className=" text-danger "
                                    color="danger"
                                    icon={cilXCircle}
                                  />
                                </button>
                              </CTooltip>
                            </CTableDataCell>
                          );
                        }
                      }
                    })}
                </CTableRow>
              ))}
            </CTableBody>
          </CTable>
        </CCol>
      </CRow>
    </CContainer>
  );
};

export default Middlewares;
