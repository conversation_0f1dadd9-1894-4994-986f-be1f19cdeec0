import React, { useEffect, useState } from "react";
import {
  CButton,
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  CForm,
  CFormTextarea,
  CRow,
} from "@coreui/react";
import MyCkEditor from "src/components/CkEditor/Editor";
import { useLocation, useNavigate } from "react-router-dom";
import ImageUploader from "src/components/fileManagers/imageUploader";
import { toast } from "react-hot-toast";
import Warning from "src/components/alerts/Warning/Warning";
import Danger from "src/components/alerts/Danger/Danger";
import { useDispatch, useSelector } from "react-redux";
import {
  createOffer,
  fetchAllAffiliationsList,
  fetchAllCategoriesList,
  fetchAllStoreCategoriesList,
  fetchAllStoresList,
  fetchStoreDetails,
  resetStoreDetails,
} from "src/redux/features";
import MultipleSelect from "src/components/stores/MultipleSelect";
import Inputs from "src/components/stores/Inputs";
import { cilCloudUpload } from "@coreui/icons";
import { removePTagsFromStartAndEnd } from "src/helperFunctions/removeTags";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import { affiliateIds } from "src/constants/affiliateIds";
import { dateInDDMMYYYYFormatInCreateOffer } from "src/helperFunctions/dateFormatter";
import { generateDeeplink, isDeeplinkSupported } from "src/utils/deeplinkGenerator";

// const currentDateTime = new Date().toISOString().slice(0, 16);
const currentDateTime = new Date()
  .toLocaleString("sv-SE", { timeZoneName: "short" })
  .replace(" ", "T")
  .slice(0, 16);
const termsTemplate =
  '<p>&nbsp;</p><blockquote><p><strong>Terms and conditions to get this offer:</strong></p><ul><li>Offer valid only till stock ends.&nbsp;</li><li>You will get cashback only if you purchased from the automatically opened merchant’s website by indiancashback.com&nbsp;</li><li>Restrictions may apply in some cases.</li><li>Cashback is not applicable if the sale is cancelled or if the goods are returned.</li><li>Cashback is often paid excluding VAT, delivery and other administrative charges.</li><li>The usage of coupon will reflect in cashback amount at the time of pending cashback amount raises to approved state.</li><li>You can raise a missing cashback ticket for your transaction within 30 days of the purchase date. Any missing cashback ticket after 30 days will not be accepted by the merchant site.</li><li>Cashback may not paid on purchases made using store credits &amp; Gift vouchers.&nbsp;</li><li>Using a Coupon found outside&nbsp;<a href="https://indiancashback.com/">indiancashback com</a>&nbsp; may void your Cashback.&nbsp;</li><li>Any fraudulent behavior may result cancellation of your Cashback and you may get blacklisted by the merchant.&nbsp;</li><li>Cashback is not applicable if the sale is made using store credits.<br>&nbsp;</li></ul><p><br>&nbsp;</p></blockquote>';
// <p>&nbsp;</p><blockquote><p><strong>Terms and conditions to get this offer:</strong></p><ul><li>Offer valid only till stock ends.<br>&nbsp;</li><li>You will get cashback only if you purchased from the automatically opened merchant’s website by indiancashback.com<br>&nbsp;</li><li>Restrictions may apply in some cases.</li><li>Cashback is not applicable if the sale is cancelled or if the goods are returned.</li><li>Cashback is often paid excluding VAT, delivery and other administrative charges.</li><li>The usage of coupon will reflect in cashback amount at the time of pending cashback amount raises to approved state.</li><li>You can raise a missing cashback ticket for your transaction within 30 days of the purchase date. Any missing cashback ticket after 30 days will not be accepted by the merchant site.</li><li>Cashback may not paid on purchases made using store credits &amp; Gift vouchers.<br>&nbsp;</li><li>Using a Coupon found outside&nbsp;<a href="https://indiancashback.com/">indiancashback com</a>&nbsp; may void your Cashback.<br>&nbsp;</li><li>Any fraudulent behavior may result cancellation of your Cashback and you may get blacklisted by the merchant.<br>&nbsp;</li><li>Cashback is not applicable if the sale is made using store credits.<br>&nbsp;</li></ul><p><br>&nbsp;</p></blockquote>

const descriptionTemplateWC = "Use given Coupon code to avail this offer";
const descriptionTemplateWOC =
  "No coupon code Required, discount will be added automatically";

function CreateOffer() {
  const [store, setStore] = useState("");
  const [userType, setUsertype] = useState("both");
  const [storeCategory, setStoreCategory] = useState(null);
  const [offerPercent, setOfferPercent] = useState("");
  const [offerAmount, setOfferAmount] = useState("");
  const [offerType, setOfferType] = useState("");
  const [affiliation, setAffiliation] = useState([]);
  const [title, setTitle] = useState("");
  const [discount, setDiscount] = useState("");
  const [offer, setOffer] = useState("");
  const [itemPrice, setItemPrice] = useState("");
  const [link, setLink] = useState("");
  const [offerLink, setOfferLink] = useState("");
  const [isCoupon, setIsCoupon] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [description, setCashbackDescription] = useState(
    isCoupon ? descriptionTemplateWC : descriptionTemplateWOC
  );
  const [mobileData, setMobileData] = useState("");
  const [stockEnds, setStocksEnds] = useState(false);

  const [dateStart, setDateStart] = useState(
    dateInDDMMYYYYFormatInCreateOffer(currentDateTime)
  );
  const [dateExpiry, setDateExpiry] = useState(
    dateInDDMMYYYYFormatInCreateOffer(currentDateTime, false)
  );

  const [repeatBy, setRepeatBy] = useState(false);
  const [hideCbTag, setHideCbTag] = useState(false);
  const [terms, setCashbackTerms] = useState(termsTemplate);
  const [productImage, setProductImage] = useState(null);
  const [categories, setCategories] = useState([]);
  const [storeCategoryOptions, setStoreCategoryOptions] = useState([]);
  const [selectedAffiliation, setSelectedAffiliation] = useState("");
  const [importantUpdate, setImportantUpdate] = useState("");

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { allStoreCategoryList } = useSelector((state) => state.storeCategory);
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);
  const { allCategoryList } = useSelector((state) => state.category);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllCategoriesList());
    dispatch(fetchAllStoreCategoriesList());
  }, [dispatch]);

  useEffect(() => {
    if (store) {
      setStoreCategoryOptions(
        allStoreCategoryList?.filter((item) => item.store === store)
      );

      allStoresList?.map((item) => {
        if (item._id === store) {
          setSelectedAffiliation(item?.affiliation);
          setLink(item.affiliateLink);
        }
      });
    }
    setCashbackDescription(
      isCoupon ? descriptionTemplateWC : descriptionTemplateWOC
    );
  }, [store, isCoupon, allStoreCategoryList, allStoresList]);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      if (!offerAmount && !offerPercent) {
        return toast.custom(
          <Danger
            message={"please add cashback amount either cashback percent"}
          />
        );
      }

      const payload = {
        store,
        storeCategory,
        offerPercent,
        offerAmount,
        offerType,
        affiliation:
          typeof affiliation === "string" ? affiliation : affiliation?.value,
        title: removePTagsFromStartAndEnd(title),
        discount,
        offer,
        itemPrice,
        link,
        isCoupon,
        couponCode,
        description: removePTagsFromStartAndEnd(description),
        keySpecs: removePTagsFromStartAndEnd(mobileData),
        dateStart,
        dateExpiry,
        categories,
        stockEnds,
        repeatBy,
        terms,
        productImage,
        importantUpdate,
        userType,
        hideCbTag,
      };

      const data = await dispatch(createOffer(payload)).unwrap();
      if (data?.success) {
        navigate("/offers/all-offers");
      }
    } catch (error) {
      if (error?.response?.status === 403) {
        return toast.custom(<Warning message={"permission denied!"} />);
      }
      toast.custom(
        <Danger message={error?.response?.data?.errors || error.message} />
      );
    }
  };

  const { storeDetails } = useSelector((state) => state.store);

  useEffect(() => {
    if (storeDetails?.affiliation) {
      setAffiliation({
        label: storeDetails?.affiliation?.name,
        value: storeDetails?.affiliation?._id,
      });
    }
  }, [storeDetails]);

  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
  };
  const handleSelectStore = (values) => {
    setStore(values?.value);
    dispatch(fetchStoreDetails(values?.value));
  };
  const handleSelectAdditionalUpto = (values) => {
    if (offerPercent && values) {
      setOffer(`${values?.label}  ${offerPercent}% cashback`);
    }
    if (offerAmount && values) {
      setOffer(`${values?.label} Rs.${offerAmount} cashback`);
    }
    setOfferType(values?.value);
  };

  const handleSelectStoreCategory = (values) => {
    setStoreCategory(values?.value);
    const [selectedCategory] = allStoreCategoryList.filter(
      (item) => item._id === values?.value
    );
    if (selectedCategory?.newUserOfferAmount) {
      setOfferAmount(selectedCategory?.newUserOfferAmount);
      setOfferPercent("");
    }
    if (selectedCategory?.newUserOfferPercent) {
      setOfferPercent(selectedCategory?.newUserOfferPercent);
      setOfferAmount("");
    }
  };

  // const handleOfferAmount = (value, type) => {
  //   if (type === "percent") {
  //     setOfferAmount("");
  //     setOfferPercent(value);
  //     setOffer(`${offerType} ${value}% cashback`);
  //   } else {
  //     setOfferAmount(value);
  //     setOffer(`${offerType} rs ${value} cashback`);
  //     setOfferPercent("");
  //   }
  // };

  const handleBaseLinkClick = async () => {
    if (store) {
      const selectedStore = allStoresList.find((item) => item._id === store);
      setLink(selectedStore.affiliateLink);
    } else {
      toast.custom(<Warning message={"Select a store to set Section Link"} />);
    }
  };

  const handleGenerateLinkClick = async () => {
    if (!store) {
      toast.custom(<Warning message={"Select a store to set Section Link"} />);
      return;
    }

    const selectedStore = allStoresList.find((item) => item._id === store);
    const updatedOfferLink = generateDeeplink({
      selectedStore,
      offerLink,
      baseLink: link
    });

    if (updatedOfferLink) {
      setLink(updatedOfferLink);
    }
  };

  const handleOfferAmount = (value, type) => {
    if (type === "percent") {
      setOffer(`${offerType == "upto" ? "Up to" : "Flat"} ${value}% cashback`);
      setOfferAmount("");
      setOfferPercent(value);
    } else {
      setOfferAmount(value);
      setOffer(
        `${offerType == "upto" ? "Up to" : "Flat"} Rs.${value} cashback`
      );
      setOfferPercent("");
    }
  };
  const location = useLocation();
  useEffect(() => {
    return () => {
      dispatch(resetStoreDetails());
    };
  }, [location]);

  return (
    <CContainer fluid>
      <CRow className="justify-content-center">
        <h3 className="text-center">Create new Offer</h3>
        <CCol lg={10}>
          <CForm onSubmit={handleSubmit}>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Select Store</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  array={allStoresList}
                  handleSelectedValue={handleSelectStore}
                  placeholder={"Select Store..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Store category</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  array={storeCategoryOptions}
                  handleSelectedValue={handleSelectStoreCategory}
                  placeholder={"Select Store Category..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Percent</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  type="text"
                  title="number only"
                  className="w-100 p-2 border rounded"
                  value={offerPercent}
                  onChange={(e) => handleOfferAmount(e.target.value, "percent")}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Amount</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  // type="number"
                  type="decimal"
                  step="0.01"
                  className="w-100 p-2 border rounded"
                  value={offerAmount}
                  onChange={(e) => handleOfferAmount(e.target.value, "amount")}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Offer Type</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  array={[
                    { name: "Flat", _id: "flat" },
                    { name: "Up to", _id: "upto" },
                  ]}
                  handleSelectedValue={handleSelectAdditionalUpto}
                  placeholder={"Select Offer Type"}
                  clearable
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">affiliation</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  key={"affiliationFromOffer"}
                  defaultValue={affiliation}
                  array={allAffiliationsList}
                  handleSelectedValue={handleSelectAffiliations}
                  placeholder={"Select Partners..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Title</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <CFormTextarea
                  placeholder="Cashback Title"
                  type="text"
                  rows={6}
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                />
              </CCol>
            </CRow>
            <Inputs
              state={discount}
              setState={setDiscount}
              title={"Discount"}
              // type={"number"}
              type="decimal"
              step="0.01"
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Offer</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <CFormTextarea
                  placeholder="Cashback Offer"
                  type="text"
                  rows={6}
                  value={offer}
                  onChange={(e) => setOffer(e.target.value)}
                  required
                />
              </CCol>
            </CRow>
            <Inputs
              state={itemPrice}
              setState={setItemPrice}
              title={"Item Price"}
              // type={"number"}
              type="decimal"
              step="0.01"
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Cashback Link</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <CFormTextarea
                  placeholder="Cashback Link"
                  type="text"
                  rows={6}
                  value={link}
                  onChange={(e) => {
                    setLink(e.target.value);
                  }}
                  required
                />
              </CCol>
            </CRow>

            <CRow className="mt-6">
              <div className="text-end  mt-[10px]">
                <CButton
                  // type="reset"
                  onClick={() => {
                    handleBaseLinkClick();
                  }}
                  className="text-center"
                  style={{
                    backgroundColor: "#4BAAC8",
                    borderColor: "#4BAAC8",
                    color: "white",
                  }}
                >
                  Base Link
                </CButton>
              </div>
            </CRow>

            {isDeeplinkSupported(storeDetails) && (
                <>
                  <CRow className="my-3 mb-8">
                    <CCol md={3} lg={3} xl={3}>
                      <p className="me-3 my-auto">Offer Link</p>
                    </CCol>
                    <CCol md={9} lg={9} xl={9}>
                      <CFormTextarea
                        placeholder="Offer Link"
                        type="text"
                        rows={6}
                        value={offerLink}
                        onChange={(e) => {
                          setOfferLink(e.target.value);
                        }}
                        // required
                      />
                    </CCol>
                  </CRow>

                  <CRow className="mt-6">
                    <div className="text-end  mt-[10px]">
                      <CButton
                        // type="reset"
                        onClick={() => {
                          handleGenerateLinkClick();
                        }}
                        className="text-center"
                        style={{
                          backgroundColor: "#4BAAC8",
                          borderColor: "#4BAAC8",
                          color: "white",
                        }}
                      >
                        Generate Link
                      </CButton>
                    </div>
                  </CRow>
                </>
              )}

            <CRow className="my-3 ">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Is Coupon Type</p>
              </CCol>
              <CCol md={8} lg={8} xl={8} className="flex">
                <div>
                  <input
                    type="radio"
                    name="isCoupon"
                    id=""
                    value={isCoupon}
                    onClick={() => {
                      setIsCoupon(false);
                      setCouponCode("");
                    }}
                    defaultChecked
                  />
                  <label htmlFor="" className="mx-1">
                    No
                  </label>
                </div>
                <div>
                  <input
                    type="radio"
                    name="isCoupon"
                    id=""
                    value={isCoupon}
                    onClick={() => setIsCoupon(true)}
                  />
                  <label htmlFor="" className="mx-1">
                    Yes
                  </label>
                </div>
              </CCol>
            </CRow>
            <Inputs
              state={couponCode}
              setState={setCouponCode}
              title={"Coupon Code"}
              type={"text"}
              disabled={!isCoupon}
            />
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto"> DesCription</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  content={description}
                  setData={setCashbackDescription}
                  toolbar={["bold", "bulletedList", "italic", "link"]}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto"> Mobile Data HTML</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  setData={setMobileData}
                  toolbar={["bold", "bulletedList", "italic", "link"]}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto"> Important Update</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  toolbar={["bulletedList"]}
                  setData={setImportantUpdate}
                />
              </CCol>
            </CRow>

            <CRow className="my-3">
              <CCol
                md={3}
                lg={3}
                xl={3}
                className="d-flex justify-content-start align-items-center"
              >
                <p className="me-3 my-auto text-capitalize ">User Type</p>
              </CCol>
              <CCol md={9} lg={9} xl={9} className=" ">
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"new"}
                    checked={userType === "new"}
                    onChange={(e) => setUsertype(e.target.value)}
                  />
                  <label htmlFor=""> New</label>
                </div>
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"existing"}
                    checked={userType === "existing"}
                    onChange={(e) => setUsertype(e.target.value)}
                  />

                  <label htmlFor=""> Existing</label>
                </div>

                <div>
                  <input
                    className={``}
                    type={"radio"}
                    value={"both"}
                    checked={userType === "both"}
                    onChange={(e) => setUsertype(e.target.value)}
                  />
                  <label htmlFor=""> Both</label>
                </div>
              </CCol>
            </CRow>

            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto"> categories</p>
              </CCol>
              <CCol md={9} lg={9} xl={9} className="border rounded">
                <MultipleSelect
                  options={allCategoryList}
                  setCategories={setCategories}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Start Date</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  type="datetime-local"
                  id="datetime"
                  name="datetime"
                  className="w-50 p-2 rounded border"
                  value={dateStart}
                  onChange={(e) => setDateStart(e.target.value)}
                  // min={currentDateTime}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Valid till stock end</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <div>
                  <input
                    type="radio"
                    name="stockEnds"
                    id=""
                    defaultChecked
                    onClick={() => setStocksEnds(false)}
                  />
                  <label htmlFor="" className="mx-1">
                    NO
                  </label>
                </div>
                <div>
                  <input
                    type="radio"
                    name="stockEnds"
                    id=""
                    onClick={() => setStocksEnds(true)}
                  />
                  <label htmlFor="" className="mx-1">
                    Yes
                  </label>
                </div>
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto"> Date of Expiry</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  className="w-50 p-2 rounded border "
                  type="datetime-local"
                  id="datetime"
                  name="datetime"
                  value={dateExpiry}
                  onChange={(e) => setDateExpiry(e.target.value)}
                  // min={currentDateTime}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Repeat Duration</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <div>
                  <input
                    type="radio"
                    name="repeatBy"
                    id=""
                    defaultChecked
                    onClick={() => setRepeatBy(false)}
                  />
                  <label htmlFor="" className="mx-1">
                    NO
                  </label>
                </div>
                <div>
                  <input
                    type="radio"
                    name="repeatBy"
                    id=""
                    onClick={() => setRepeatBy(true)}
                  />
                  <label htmlFor="" className="mx-1">
                    Yes
                  </label>
                </div>
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Hide Cashback Tag</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <div>
                  <input
                    type="radio"
                    name="hideCbTag"
                    id=""
                    defaultChecked
                    onClick={() => setHideCbTag(false)}
                  />
                  <label htmlFor="" className="mx-1">
                    NO
                  </label>
                </div>
                <div>
                  <input
                    type="radio"
                    name="hideCbTag"
                    id=""
                    onClick={() => setHideCbTag(true)}
                  />
                  <label htmlFor="" className="mx-1">
                    Yes
                  </label>
                </div>
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">CB Product Image</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <ImageUploader
                  setFile={setProductImage}
                  icon={cilCloudUpload}
                  keyword={"upload image"}
                />
                {productImage?.secureUrl && (
                  <img
                    src={productImage?.secureUrl}
                    width={100}
                    className="w-50"
                    alt="product"
                  />
                )}
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Terms and Conditions</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor
                  setData={setCashbackTerms}
                  content={terms}
                  toolbar={["bold", "bulletedList", "italic", "link"]}
                />
              </CCol>
            </CRow>
            <div className="  text-center mt-4 pt-4">
              <CButton
                onClick={() => navigate("/offers/all-offers")}
                className="text-light mx-1"
                type="reset"
                color="danger"
              >
                Cancel
              </CButton>
              <CButton
                className="text-light"
                type="submit"
                color="success mx-1"
              >
                Create
              </CButton>
            </div>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default CreateOffer;
