import React, { useEffect, useState } from "react";
import {
  CButton,
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  <PERSON>orm,
  CFormCheck,
  CFormInput,
  CFormTextarea,
  CRow,
} from "@coreui/react";
import MyCkEditor from "src/components/CkEditor/Editor";
import { useNavigate, useParams } from "react-router-dom";
import ImageUploader from "src/components/fileManagers/imageUploader";
import { toast } from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";
import {
  convertUTCtoLocal,
  dateInDDMMYYYYFormat,
} from "src/helperFunctions/dateFormatter";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllAffiliationsList,
  fetchAllCategoriesList,
  fetchAllStoreCategoriesList,
  fetchAllStoresList,
  fetchOfferDetails,
  fetchStoreDetails,
  updateOffer,
} from "src/redux/features";
import { cilCloudUpload } from "@coreui/icons";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import { removePTagsFromStartAndEnd } from "src/helperFunctions/removeTags";
import MultipleSelect from "src/components/stores/MultipleSelect";
import Inputs from "src/components/stores/Inputs";
import Warning from "src/components/alerts/Warning/Warning";
import { affiliateIds } from "src/constants/affiliateIds";
import { generateDeeplink, isDeeplinkSupported } from "src/utils/deeplinkGenerator";

const currentDateTime = new Date()
  .toLocaleString("sv-SE", { timeZoneName: "short" })
  .replace(" ", "T")
  .slice(0, 16);

function EditOffer() {
  const [store, setStore] = useState("");
  const [userType, setUsertype] = useState("both");
  const [storeCategory, setStoreCategory] = useState("");
  const [offerPercent, setOfferPercent] = useState("");
  const [offerAmount, setOfferAmount] = useState("");
  const [additionalUpto, setAdditionalUpto] = useState("");
  const [affiliation, setAffiliation] = useState([]);
  const [title, setTitle] = useState("");
  const [discount, setDiscount] = useState("");
  const [offer, setOffer] = useState("");
  const [generatedUrl, setGeneratedUrl] = useState("");
  const [itemPrice, setItemPrice] = useState("");
  const [link, setLink] = useState("");
  const [offerLink, setOfferLink] = useState("");
  const [isCoupon, setIsCoupon] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [description, setDescription] = useState("");
  const [mobileData, setMobileData] = useState("");
  const [tickAllStoreCat, setTickAllStoreCat] = useState(false);
  const [dateStart, setDateStart] = useState("");
  const [dateExpiry, setDateExpiry] = useState("");
  const [defaultCategories, setDefaultCategories] = useState([]);
  const [stockEnds, setStocksEnds] = useState(false);
  const [repeatBy, setRepeatBy] = useState(false);
  const [hideCbTag, setHideCbTag] = useState(false);
  const [howToGet, setHowToGet] = useState("");
  const [terms, setTerms] = useState("");
  const [productImage, setProductImage] = useState(null);
  const [categories, setCategories] = useState([]);
  const [defaultStoreCat, setDefaultStoreCat] = useState("");
  const [defaultStore, setDefaultStore] = useState({ label: "", value: "" });
  const [defaultAffiliation, setDefaultAffiliation] = useState("");
  const [defaultAdditionalUpto, setDefaultAdditionalUpto] = useState("");
  const [importantUpdate, setImportantUpdate] = useState("");

  const [catOptions, setCatOptions] = useState([]);
  const { offerId } = useParams();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allStoresList } = useSelector((state) => state.store);

  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { offerDetails } = useSelector((state) => state.offer);
  const { allStoreCategoryList } = useSelector((state) => state.storeCategory);
  const { allCategoryList } = useSelector((state) => state.category);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllStoreCategoriesList());
    dispatch(fetchAllCategoriesList());
    if (offerId) {
      dispatch(fetchOfferDetails(offerId));
    }
  }, [dispatch, offerId]);

  useEffect(() => {
    if (offerDetails) {
      setUsertype(offerDetails?.userType);
      setStore(offerDetails?.store?._id);
      dispatch(fetchStoreDetails(offerDetails?.store?._id));
      setDefaultStore({
        label: offerDetails?.store?.name,
        value: offerDetails?.store?._id,
      });
      setStoreCategory(offerDetails?.storeCategory);
      setDefaultStoreCat({
        label: offerDetails?.storeCategory?.name,
        value: offerDetails?.storeCategory?._id,
      });
      setDefaultAdditionalUpto({
        label: offerDetails?.additionalUpto,
        value: offerDetails?.additionalUpto,
      });
      setAdditionalUpto(offerDetails?.offerType);
      setIsCoupon(offerDetails?.isCoupon);
      setCouponCode(offerDetails?.couponCode);
      setOfferPercent(offerDetails?.offerPercent);
      setOfferAmount(offerDetails?.offerAmount);
      setAffiliation(offerDetails?.affiliation?._id);
      setDefaultAffiliation({
        value: offerDetails?.affiliation?._id,
        label: offerDetails?.affiliation?.name,
      });
      setTitle(offerDetails?.title);
      setDiscount(offerDetails?.discount);
      setDescription(offerDetails?.description);
      setProductImage(offerDetails?.productImage);
      setStocksEnds(offerDetails?.stockEnds);
      setDateStart(offerDetails?.dateStart);
      setDateExpiry(offerDetails?.dateExpiry);
      setOffer(offerDetails?.offer);
      setLink(offerDetails?.link);
      setHowToGet(offerDetails?.howToGet);
      setTerms(offerDetails?.terms);
      setItemPrice(offerDetails?.itemPrice);
      setGeneratedUrl(offerDetails?.offerUrl);
      setMobileData(offerDetails?.keySpecs);
      setDefaultCategories(offerDetails?.categories);
      setImportantUpdate(offerDetails?.importantUpdate);
      setRepeatBy(offerDetails?.repeatBy == "true" ? true : false);
      setHideCbTag(offerDetails?.hideCbTag);
    }
  }, [offerDetails]);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      if (!offerAmount && !offerPercent) {
        return toast.custom(
          <Danger message={"please add cashback amount either percent"} />
        );
      }
      const payload = {
        store,
        storeCategory,
        offerAmount,
        offerPercent,
        offerType: additionalUpto,
        affiliation,
        title: removePTagsFromStartAndEnd(title),
        discount,
        offer,
        offerUrl: generatedUrl,
        itemPrice: itemPrice,
        link,
        isCoupon,
        couponCode,
        description: removePTagsFromStartAndEnd(description),
        keySpecs: mobileData && removePTagsFromStartAndEnd(mobileData),
        tickAllStoreCat,
        dateStart,
        dateExpiry,
        categories,
        stockEnds: stockEnds,
        repeatBy: repeatBy,
        hideCbTag,
        howToGet: howToGet && removePTagsFromStartAndEnd(howToGet),
        terms: removePTagsFromStartAndEnd(terms),
        productImage,
        importantUpdate: removePTagsFromStartAndEnd(importantUpdate),
        userType,
      };
      // dispatch(updateOffer({ offerId, payload }));

      const data = await dispatch(updateOffer({ offerId, payload })).unwrap();
      // console.log(data, "data");
      if (data?.success) {
        navigate(-1);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const { storeDetails } = useSelector((state) => state.store);

  const handleSelectStoreCats = (e) => {
    try {
      e.preventDefault();
      if (!store) {
        return alert("please select a store");
      }

      const [cat] = allStoresList.filter((data) => data._id === store);
      cat.categories.map((i) => {
        catOptions.map((data) => {
          if (data._id === i) {
            data.checked = true;
            data.subCategories.map((sub) => {});
          }
        });
      });
    } catch (error) {
      toast.custom(<Danger message={error.message} />);
    }
  };
  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
  };
  const handleSelectStore = (values) => {
    setStore(values?.value);
  };
  const handleSelectStoreCategory = (values) => {
    setStoreCategory(values?.value);
  };
  const handleSelectAdditionalUpto = (values) => {
    if (offerPercent && values) {
      setOffer(`${values?.label}  ${offerPercent}% cashback`);
    }
    if (offerAmount && values) {
      setOffer(`${values?.label} Rs.${offerAmount} cashback`);
    }
    setAdditionalUpto(values?.value);
  };
  const handleOfferAmount = (value, type) => {
    if (type === "percent") {
      setOffer(
        `${additionalUpto == "upto" ? "Up to" : "Flat"} ${value}% cashback`
      );
      setOfferAmount("");
      setOfferPercent(value);
    } else {
      setOfferAmount(value);
      setOffer(
        `${additionalUpto == "upto" ? "Up to" : "Flat"} Rs.${value} cashback`
      );
      setOfferPercent("");
    }
  };

  const handleBaseLinkClick = async () => {
    if (store) {
      const selectedStore = allStoresList.find((item) => item._id === store);
      setLink(selectedStore.affiliateLink);
    } else {
      toast.custom(<Warning message={"Select a store to set Section Link"} />);
    }
  };

  const handleGenerateLinkClick = async () => {
    if (!store) {
      toast.custom(<Warning message={"Select a store to set Section Link"} />);
      return;
    }

    const selectedStore = allStoresList.find((item) => item._id === store);
    const updatedOfferLink = generateDeeplink({
      selectedStore,
      offerLink,
      baseLink: link
    });

    if (updatedOfferLink) {
      setLink(updatedOfferLink);
    }
  };

  return (
    <div className="bg-light  d-flex flex-row ">
      <CContainer fluid>
        <CRow className="d-flex justify-content-center">
          <h3 className="text-center">Edit Cashback</h3>
          <CCol md={11} lg={8}>
            <CForm onSubmit={handleSubmit}>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Select Store</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndSelect
                    id="selectStore"
                    defaultValue={defaultStore}
                    array={allStoresList}
                    handleSelectedValue={handleSelectStoreCategory}
                    placeholder={"Select Store..."}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Store category</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndSelect
                    id="selectStoreCategory"
                    defaultValue={defaultStoreCat}
                    array={allStoreCategoryList}
                    handleSelectedValue={handleSelectStore}
                    placeholder={"Select Store Category..."}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Cashback Percent</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <input
                    type="text"
                    id="cashbackPercent"
                    title="number only"
                    className="w-100 p-2 border rounded"
                    value={offerPercent}
                    onChange={(e) =>
                      handleOfferAmount(e.target.value, "percent")
                    }
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Cashback Amount</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <input
                    // type="number"
                    type="decimal"
                    step="0.01"
                    id="cashbackAmount"
                    className="w-100 p-2 border rounded"
                    value={offerAmount}
                    onChange={(e) =>
                      handleOfferAmount(e.target.value, "amount")
                    }
                  />
                </CCol>
              </CRow>

              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Offer Type</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndSelect
                    array={[
                      { name: "Flat", _id: "flat" },
                      { name: "Up to", _id: "upto" },
                    ]}
                    defaultValue={
                      additionalUpto == "upto"
                        ? { label: "Up to", value: "upto" }
                        : { label: "Flat", value: "flat" }
                      // defaultAdditionalUpto
                    }
                    handleSelectedValue={handleSelectAdditionalUpto}
                    placeholder={"Select Additional / Up To..."}
                    id="additionalUpto"
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">affiliation</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <SearchAndSelect
                    id="affiliationSelection"
                    defaultValue={defaultAffiliation}
                    array={allAffiliationsList}
                    handleSelectedValue={handleSelectAffiliations}
                    placeholder={"Select Partners..."}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Cashback Title</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  {/* <MyCkEditor
                    content={title}
                    setData={setTitle}
                    toolbar={["bold"]}
                    headingOptions={[]}
                  /> */}
                  <CFormTextarea
                    placeholder="Cashback Title"
                    type="text"
                    rows={6}
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    required
                  />
                </CCol>
              </CRow>
              <Inputs
                state={discount}
                setState={setDiscount}
                title={"Discount"}
                // type={"number"}
                type="decimal"
                step="0.01"
                id="cashbackTitle"
              />
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Cashback Offer</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <CFormTextarea
                    placeholder="Cashback Offer"
                    type="text"
                    value={offer}
                    rows={6}
                    onChange={(e) => setOffer(e.target.value)}
                    required
                    id="cashbackOffer"
                  />
                </CCol>
              </CRow>
              {/* <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Generated URL</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <CFormTextarea
                    placeholder="Generated URL"
                    value={generatedUrl}
                    type="text"
                    rows={6}
                    onChange={(e) => setGeneratedUrl(e.target.value)}
                  />
                </CCol>
              </CRow> */}
              <Inputs
                state={itemPrice}
                setState={setItemPrice}
                title={"Item Price"}
                // type={"number"}
                type="decimal"
                step="0.01"
              />
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Cashback Link</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <CFormTextarea
                    placeholder="Generated URL"
                    value={link}
                    type="text"
                    rows={6}
                    onChange={(e) => setLink(e.target.value)}
                  />
                </CCol>
              </CRow>

              <CRow className="mt-6">
                <div className="text-end  mt-[10px]">
                  <CButton
                    // type="reset"
                    onClick={() => {
                      handleBaseLinkClick();
                    }}
                    className="text-center"
                    style={{
                      backgroundColor: "#4BAAC8",
                      borderColor: "#4BAAC8",
                      color: "white",
                    }}
                  >
                    Base Link
                  </CButton>
                </div>
              </CRow>
              {isDeeplinkSupported(storeDetails) && (
                  <>
                    <CRow className="my-3 mb-8">
                      <CCol md={3} lg={3} xl={3}>
                        <p className="me-3 my-auto">Offer Link</p>
                      </CCol>
                      <CCol md={9} lg={9} xl={9}>
                        <CFormTextarea
                          placeholder="Offer Link"
                          type="text"
                          rows={6}
                          value={offerLink}
                          onChange={(e) => {
                            setOfferLink(e.target.value);
                          }}
                          // required
                        />
                      </CCol>
                    </CRow>

                    <CRow className="mt-6">
                      <div className="text-end  mt-[10px]">
                        <CButton
                          // type="reset"
                          onClick={() => {
                            handleGenerateLinkClick();
                          }}
                          className="text-center"
                          style={{
                            backgroundColor: "#4BAAC8",
                            borderColor: "#4BAAC8",
                            color: "white",
                          }}
                        >
                          Generate Link
                        </CButton>
                      </div>
                    </CRow>
                  </>
                )}

              <CRow className="my-3 ">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Is Coupon Type</p>
                </CCol>
                <CCol md={8} lg={8} xl={8} className="flex">
                  <CFormCheck
                    type="radio"
                    name="flexRadioD"
                    id="flexRadioDefault1"
                    label="No"
                    onChange={() => setIsCoupon(false)}
                    checked={isCoupon ? false : true}
                  />
                  <CFormCheck
                    type="radio"
                    name="flexRadioD"
                    id="flexRadioDefault2"
                    label="Yes"
                    onChange={() => setIsCoupon(true)}
                    checked={isCoupon}
                  />
                </CCol>
              </CRow>
              <Inputs
                state={couponCode}
                setState={setCouponCode}
                title={"Coupon Code"}
                type={"text"}
                disabled={!isCoupon}
              />
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto"> DesCription</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={description} setData={setDescription} />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto"> Mobile Data HTML</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={mobileData} setData={setMobileData} />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto"> Important Update</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor
                    content={importantUpdate}
                    toolbar={["bulletedList"]}
                    setData={setImportantUpdate}
                  />
                </CCol>
              </CRow>

              <CRow className="my-3">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">User Type</p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className=" ">
                  <div>
                    <input
                      className={` `}
                      type={"radio"}
                      value={"new"}
                      checked={userType === "new"}
                      onChange={(e) => setUsertype(e.target.value)}
                    />
                    <label htmlFor=""> New</label>
                  </div>
                  <div>
                    <input
                      className={` `}
                      type={"radio"}
                      value={"existing"}
                      checked={userType === "existing"}
                      onChange={(e) => setUsertype(e.target.value)}
                    />

                    <label htmlFor=""> Existing</label>
                  </div>

                  <div>
                    <input
                      className={``}
                      type={"radio"}
                      value={"both"}
                      checked={userType === "both"}
                      onChange={(e) => setUsertype(e.target.value)}
                    />
                    <label htmlFor=""> Both</label>
                  </div>
                </CCol>
              </CRow>

              {/* <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Tick All Store Categories</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <CFormCheck
                    type="checkbox"
                    name="flexRadioDefaul"
                    id="flexRadioDefault3"
                    onClick={handleSelectStoreCats}
                  />
                </CCol>
              </CRow> */}
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto"> categories</p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="border rounded">
                  <MultipleSelect
                    defaultCategories={offerDetails?.categories}
                    options={allCategoryList}
                    setCategories={setCategories}
                    defaultCategoriesType={"idArray"}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Start Date</p>
                </CCol>
                <CCol className="d-flex" md={9} lg={9} xl={9}>
                  <input
                    type="datetime-local"
                    id="datetime"
                    name="datetime"
                    className="w-50 p-2 rounded border"
                    value={dateStart ? convertUTCtoLocal(dateStart) : ""}
                    onChange={(e) => setDateStart(e.target.value)}
                    // min={currentDateTime}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Valid Still Stocks End</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <CFormCheck
                    type="radio"
                    name="flex1"
                    id="flexRadioDefault5"
                    label="No"
                    onChange={() => setStocksEnds(false)}
                    checked={stockEnds ? false : true}
                  />
                  <CFormCheck
                    type="radio"
                    name="flex2"
                    id="flexRadioDefault6"
                    label="Yes"
                    onChange={() => setStocksEnds(true)}
                    checked={stockEnds}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto"> Date of Expiry</p>
                </CCol>
                <CCol className="d-flex" md={9} lg={9} xl={9}>
                  <input
                    className="w-50 p-2 rounded border "
                    type="datetime-local"
                    id="datetime"
                    name="datetime"
                    value={dateExpiry ? convertUTCtoLocal(dateExpiry) : ""}
                    onChange={(e) => setDateExpiry(e.target.value)}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Repeat Duration </p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <CFormCheck
                    type="radio"
                    name="flexRepeat"
                    id="flexRadioDefaultRepeat"
                    label="No"
                    // defaultChecked
                    checked={repeatBy ? false : true}
                    onChange={() => setRepeatBy(false)}
                  />
                  <CFormCheck
                    type="radio"
                    name="flexRepeat"
                    id="flexRadioDefaultRepeat"
                    label="Yes"
                    checked={repeatBy}
                    onChange={() => setRepeatBy(true)}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Hide Cb Tag</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <CFormCheck
                    type="radio"
                    name="flex"
                    id="flexRadioDefaultDuration"
                    label="No"
                    checked={hideCbTag ? false : true}
                    onChange={() => setHideCbTag(false)}
                  />
                  <CFormCheck
                    type="radio"
                    name="flex"
                    id="flexRadioDefaultDuration6"
                    label="Yes"
                    checked={hideCbTag}
                    onChange={() => setHideCbTag(true)}
                  />
                </CCol>
              </CRow>
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">CB Product Image</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <ImageUploader
                    setFile={setProductImage}
                    icon={cilCloudUpload}
                    keyword={"upload image"}
                  />
                  {productImage?.secureUrl && (
                    <img
                      src={productImage?.secureUrl}
                      width={100}
                      className="w-50 m-2 rounded shadow"
                      alt="product image"
                    />
                  )}
                </CCol>
              </CRow>
              {/* <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">How to Get</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={howToGet} setData={setHowToGet} />
                </CCol>
              </CRow> */}
              <CRow className="my-3">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto">Terms and Conditions</p>
                </CCol>
                <CCol md={9} lg={9} xl={9}>
                  <MyCkEditor content={terms} setData={setTerms} />
                </CCol>
              </CRow>
              <div className="  text-center mt-4 pt-4">
                <CButton
                  onClick={() => navigate("/cashbacks/all-cashbacks")}
                  className="text-light mx-1"
                  type="reset"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  className="text-light"
                  type="submit"
                  color="success mx-1"
                >
                  Update
                </CButton>
              </div>
            </CForm>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
}

export default EditOffer;
