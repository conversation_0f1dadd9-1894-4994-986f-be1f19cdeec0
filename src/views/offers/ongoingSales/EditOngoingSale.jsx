import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import InputField from "src/components/inputs/InputField";
import { useDispatch, useSelector } from "react-redux";
import ImageUploader from "src/components/fileManagers/imageUploader";
import { cilCloudUpload } from "@coreui/icons";
import {
  createOngoingSale,
  fetchOngoingSaleDetails,
  updateOngoingSale,
} from "src/redux/features/ongoingSaleOffers";
import toast from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";
import SearchAndMultiSelect from "src/components/select/SearchAndMultiSelect";
import { fetchAllOffersList } from "src/redux/features";
import {
  convertUT<PERSON>toLocal,
  dateInDDMMYYYYFormat,
} from "src/helperFunctions/dateFormatter";
const currentDateTime = new Date().toISOString().slice(0, 16);
const schema = yup
  .object({
    saleName: yup.string().required("Please enter sale name"),
    saleStartDate: yup
      .date("invalid date format")
      .required("Please enter sale start date"),
    saleEndDate: yup
      .date("invalid date format")
      .required("Please enter sale end date"),
  })
  .required();

function EditOngoingSale() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [saleLogo, setSaleLogo] = useState("");
  const [offers, setOffers] = useState([]);
  const [offerOptions, setOfferOptions] = useState([]);
  const { saleId } = useParams();

  const { allOffersList } = useSelector((state) => state.offer);

  const { ongoingSaleDetails } = useSelector((state) => state.ongoingSale);

  useEffect(() => {
    dispatch(fetchAllOffersList({ active: true }));
    if (saleId) {
      dispatch(fetchOngoingSaleDetails(saleId));
    }
  }, [dispatch, saleId]);

  useEffect(() => {
    if (ongoingSaleDetails) {
      setOffers(ongoingSaleDetails?.offers?.map((item) => item._id));
      setOfferOptions(
        ongoingSaleDetails?.offers?.map((item) => {
          const offer = allOffersList.find((offer) => offer._id === item._id);
          return { label: offer ? offer.title : "Unknown", value: item._id };
        })
      );
      // setOfferOptions(
      //   ongoingSaleDetails?.offers?.map((item) => {
      //     return { label: item.cashbackTitle, value: item._id };
      //   })
      // );
      setSaleLogo(ongoingSaleDetails?.saleLogo);
      reset(
        {
          saleName: ongoingSaleDetails?.saleName,
          saleStartDate: convertUTCtoLocal(ongoingSaleDetails?.saleStartDate),
          saleEndDate: convertUTCtoLocal(ongoingSaleDetails?.saleEndDate),
        },
        { keepDirty: true, keepErrors: true }
      );
    }
  }, [ongoingSaleDetails]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const onSubmit = async (data) => {
    if (!saleLogo) {
      return toast.custom(<Danger message={"please upload sale logo"} />);
    }
    const payload = {
      ...data,
      saleLogo,
      offers,
    };
    const dataRes = await dispatch(
      updateOngoingSale({ saleId, payload })
    ).unwrap();
    if (dataRes.success) {
      navigate("/offers/ongoing-sales/all-sales");
    }
  };
  const handleSelectOffers = (values) => {
    setOffers(values.map((item) => item.value));
  };
  return (
    <div>
      <CContainer>
        <CRow className="justify-content-center">
          <CCol md={9} lg={7} xl={6}>
            <CForm onSubmit={handleSubmit(onSubmit)}>
              <h2>Update Ongoing Sale</h2>
              <InputField
                state={"saleName"}
                title={"Sale Name"}
                type={"text"}
                setState={register}
                error={errors?.saleName?.message}
              />
              <InputField
                state={"saleStartDate"}
                title={"Sale Start Date"}
                type={"datetime-local"}
                setState={register}
                error={errors?.saleStartDate?.message}
                currentDateTime={currentDateTime}
              />
              <InputField
                state={"saleEndDate"}
                title={"Sale End Date"}
                type={"datetime-local"}
                setState={register}
                error={errors?.saleEndDate?.message}
                currentDateTime={currentDateTime}
              />

              <CRow className="my-5">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto text-capitalize ">Offers</p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <SearchAndMultiSelect
                    defaultValue={offerOptions}
                    array={allOffersList}
                    handleSelectedValue={handleSelectOffers}
                    placeholder={"Select offers"}
                  />
                </CCol>
              </CRow>
              <CRow className="my-5">
                <CCol md={3} lg={3} xl={3}>
                  <p className="me-3 my-auto text-capitalize ">
                    Ongoing Sale Logo
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <ImageUploader
                    icon={cilCloudUpload}
                    setFile={setSaleLogo}
                    keyword={"update sale logo"}
                  />
                  {saleLogo && (
                    <img
                      width={200}
                      className="m-2 shadow"
                      src={saleLogo?.secureUrl}
                    />
                  )}
                </CCol>
              </CRow>
              <div className="mx-auto d-flex mt-3">
                <CButton
                  onClick={() => navigate("/partners/all-partners")}
                  className="text-white ms-auto mx-1"
                  type="reset"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  className="text-white me-auto mx-1"
                  type="submit"
                  color="success"
                >
                  Update
                </CButton>
              </div>
            </CForm>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
}

export default EditOngoingSale;
