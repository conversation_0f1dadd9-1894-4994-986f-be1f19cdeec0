import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import {
  CButton,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import CIcon from "@coreui/icons-react";
import {
  cilLockLocked,
  cilLockUnlocked,
  cilPencil,
  cilTrash,
} from "@coreui/icons";
import { dateFormatter } from "src/helperFunctions/dateFormatter";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllQuickAccess,
  updateQuickAccessActiveStatus,
  deleteQuickAccess,
  handleOpenModal,
} from "src/redux/features";
import ReadMore from "src/components/text/ReadMore";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

const AllQuickAccess = () => {
  const navigate = useNavigate();
  // password reset
  const [quickAccess, setQuickAccess] = useState("");
  // delete alert
  const [visible, setVisible] = useState(false);
  const dispatch = useDispatch();
  const { allQuickAccess, page, pages, pageSize, loading } = useSelector(
    (state) => state.quickAccess
  );

  useEffect(() => {
    dispatch(fetchAllQuickAccess({}));
  }, [dispatch]);

  const handleBlock = async (id) => {
    dispatch(updateQuickAccessActiveStatus(id));
  };

  const handleDeleteClick = (data) => {
    setQuickAccess(data);
    setVisible(true);
  };

  const deleteQuickAccessClick = async () => {
    dispatch(deleteQuickAccess(quickAccess._id));
  };
  const handlePagination = (value) => {
    dispatch(fetchAllQuickAccess({ page: value }));
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="mb-3 text-end ">
          <CButton
            onClick={() => navigate("/quick-access/create")}
            color="primary"
            className=""
          >
            Create Quick Access
          </CButton>
        </div>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell className="text-center">No</CTableHeaderCell>
              <CTableHeaderCell>Title</CTableHeaderCell>
              <CTableHeaderCell>Icon</CTableHeaderCell>
              <CTableHeaderCell>Redirect Url</CTableHeaderCell>
              <CTableHeaderCell>CreatedBy</CTableHeaderCell>
              <CTableHeaderCell>CreatedAt</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allQuickAccess?.map((item, index) => (
                <CTableRow
                  v-for="item in tableItems"
                  className=" "
                  color={!item.active ? "danger" : ""}
                  key={item.uid}
                >
                  <CTableDataCell>
                    <p>{item?.uid}</p>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <p>{item.title}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <img
                      className=" shadow rounded"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        dispatch(
                          handleOpenModal({ image: item?.icon?.secureUrl })
                        )
                      }
                      width={50}
                      src={item?.icon?.secureUrl}
                      alt=""
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    <ReadMore text={item.redirectUrl} />
                  </CTableDataCell>
                  <CTableDataCell className="">
                    <div className="text-capitalize">
                      {item.createdBy?.name}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{dateFormatter(item?.createdAt)}</div>
                  </CTableDataCell>

                  <CTableDataCell className="">
                    <CTooltip content="edit quick access details">
                      <button
                        type="button"
                        className=" text-primary  border rounded shadow px-2 py-1 mx-1"
                        onClick={() =>
                          navigate(`/quick-access/edit/${item._id}`)
                        }
                      >
                        <CIcon icon={cilPencil} />
                      </button>
                    </CTooltip>
                    <CTooltip content={item.active ? "block " : "unblock "}>
                      <button
                        type="button"
                        className={`${
                          !item.active ? "text-danger " : " text-success"
                        } border rounded shadow px-2 py-1 mx-1`}
                        onClick={() => handleBlock(item._id)}
                      >
                        {!item.active ? (
                          <CIcon icon={cilLockLocked} />
                        ) : (
                          <CIcon icon={cilLockUnlocked} />
                        )}
                      </button>
                    </CTooltip>
                    <CTooltip content="delete admin">
                      <button
                        type="button"
                        className=" text-danger  border rounded shadow px-2 py-1 mx-1"
                        onClick={() => handleDeleteClick(item)}
                      >
                        <CIcon icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Deleting quick access is irreversible. Proceed with caution."}
        setState={deleteQuickAccessClick}
        visible={visible}
        setVisible={setVisible}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
};

export default AllQuickAccess;
