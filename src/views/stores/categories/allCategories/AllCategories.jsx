import { cilArrow<PERSON>ottom, cilPen, cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  <PERSON><PERSON>,
  CContainer,
  CFormCheck,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import "../../store.css";
import ReadMore from "src/components/text/ReadMore";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteStoreCategory,
  fetchAllStoreCategories,
  fetchAllStoresList,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";

function AllCategories() {
  const [search, setSearch] = useState("");
  const [name, setName] = useState("");
  const [store, setStore] = useState("");
  const [app, setApp] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { page, pages, pageSize, allStoreCategory, loading } = useSelector(
    (state) => state.storeCategory
  );
  const { allStoresList } = useSelector((state) => state.store);

  useEffect(() => {
    dispatch(fetchAllStoresList());
  }, [dispatch]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const storeParam = searchParams.get("store") || "";
    setStore(storeParam);
    dispatch(fetchAllStoreCategories({ store: storeParam, search, app, name }));
  }, [location.search, dispatch, search, app, name]);

  const handlePagination = async (value) => {
    dispatch(
      fetchAllStoreCategories({ page: value, search, app, store, name })
    );
  };
  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllStoreCategories({ search: value, app, store, name }));
  };
  const handleSetApp = async (value) => {
    setApp(value);
    dispatch(fetchAllStoreCategories({ search, app: value, name, store }));
  };

  const handleDeleteStoreCategory = async (catId) => {
    dispatch(deleteStoreCategory(catId));
  };

  const handleSelectStore = (values) => {
    const searchParams = new URLSearchParams(location.search);

    if (values?.value) {
      setStore(values.value);
      searchParams.set("store", values.value);
    } else {
      setStore("");
      searchParams.delete("store");
    }

    console.log("🚀 ~ navigate ~ `${location.pathname}?${searchParams.toString()}`:", `${location.pathname}?${searchParams.toString()}`)
    navigate(`${location.pathname}?${searchParams.toString()}`, {
      replace: true,
    });

    dispatch(
      fetchAllStoreCategories({ store: values?.value || "", search, app, name })
    );
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="">
        <CCol
          xs={12}
          sm={12}
          md={5}
          className="my-2 d-flex justify-content-start align-items-center "
        >
          <input
            type="text"
            className="w-50 border rounded py-1 px-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol sm={12} md={5} className="ms-auto text-end">
          <button
            type="button"
            className="btn btn-primary btn-sm px-4 py-2"
            onClick={() => navigate("/store-categories/create-store-category")}
          >
            Create Store Category
          </button>
        </CCol>
      </CRow>
      <CRow className="my-2 border rounded p-2 py-3">
        <CCol xs={12} sm={6} md={4} xl={4} className="">
          <SearchAndSelect
            array={allStoresList}
            handleSelectedValue={handleSelectStore}
            placeholder={"Select Store..."}
            defaultValue={
              allStoresList.find((item) => item._id === store)
                ? {
                  value: allStoresList.find((item) => item._id === store)._id,
                  label: allStoresList.find((item) => item._id === store)
                    .name,
                }
                : null
            }
            clearable
          />
        </CCol>
        <CCol xs={12} sm={6} md={4} xl={4} className="">
          <div className="m-1">
            <p className="h6">App / Desktop</p>
          </div>

          <div className="d-flex w-50 justify-content-around">
            <CFormCheck
              type="radio"
              name="exampleRadios"
              id="exampleRadios1"
              value={app}
              onClick={() => handleSetApp("app")}
              label="App"
              checked={app === "app"}
            />
            <CFormCheck
              type="radio"
              name="exampleRadios"
              id="exampleRadios2"
              value={app}
              onClick={() => handleSetApp("website")}
              label="Website"
              checked={app === "website"}
            />
          </div>
        </CCol>
      </CRow>

      <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={2}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-end  p-2 " sm={10}>
          <button
            type="button"
            className={`${name ? "border-bottom border-primary border-3 bg-none" : " "
              }  `}
            onClick={() => setName((name) => (name === 1 ? 0 : 1))}
          >
            {" "}
            name{" "}
            <CIcon
              className={`${name ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="m-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>NO</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
              <CTableHeaderCell>store</CTableHeaderCell>
              <CTableHeaderCell>category</CTableHeaderCell>
              <CTableHeaderCell>App/Desktop</CTableHeaderCell>
              <CTableHeaderCell>Affiliations</CTableHeaderCell>
              <CTableHeaderCell>Link</CTableHeaderCell>
              <CTableHeaderCell>Getting new User</CTableHeaderCell>
              <CTableHeaderCell>Getting old User</CTableHeaderCell>
              <CTableHeaderCell>Giving new User </CTableHeaderCell>
              <CTableHeaderCell>Giving old User</CTableHeaderCell>
              <CTableHeaderCell>changed By</CTableHeaderCell>
              <CTableHeaderCell>notes</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allStoreCategory?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  color={item.active ? " " : "danger"}
                  key={item._id}
                >
                  <CTableDataCell>
                    <div>{item?.uid} </div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center ">
                    <button
                      onClick={() =>
                        navigate(
                          `/store-categories/edit-store-category/${item._id}`
                        )
                      }
                      className=" text-info shadow border rounded px-2 py-1 mb-2"
                    >
                      <CIcon size="sm" className="w-100" icon={cilPen} />
                    </button>
                    <button
                      onClick={() => handleDeleteStoreCategory(item._id)}
                      className={`${!item.active ? " text-secondary" : "text-danger"
                        } shadow border rounded px-2 py-1 mb-2 `}
                    >
                      {" "}
                      <CIcon size="sm" className="w-100 " icon={cilTrash} />
                    </button>
                  </CTableDataCell>

                  <CTableDataCell>
                    <div>{item?.store?.name} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <ReadMore text={item?.name} />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.device} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.affiliation?.name} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.sectionLink} />
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.gettingNewUserRate} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.gettingOldUserRate} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.newUserOfferAmount || item?.newUserOfferPercent}{" "}
                      {item?.newUserOfferPercent && "%"}{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.oldUserOfferAmount || item?.oldUserOfferPercent}{" "}
                      {item?.oldUserOfferPercent && "%"}{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.updatedBy?.name} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <ReadMore text={item?.notes} />
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllCategories;
