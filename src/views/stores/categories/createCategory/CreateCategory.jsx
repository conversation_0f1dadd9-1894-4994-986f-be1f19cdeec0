import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>r, <PERSON><PERSON>, CRow } from "@coreui/react";
import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import Warning from "src/components/alerts/Warning/Warning";
import MyCkEditor from "src/components/CkEditor/Editor";
import { useDispatch, useSelector } from "react-redux";
import {
  createStoreCategory,
  fetchAllAffiliationsList,
  fetchAllStoresList,
  fetchStoreDetails,
  resetStoreDetails,
} from "src/redux/features";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import TextAreaField from "src/components/inputs/TextAreaFiled";
import InputField from "src/components/inputs/InputField";
import SearchAndSelect from "src/components/select/SearchAndSelect";

const categoryNotes = `<p>&nbsp;</p><blockquote><p><strong>Fashion and Apparel:</strong></p><ul><li><i>Description:</i> Trendsetting styles and clothing for diverse tastes.</li><li><i>Key Points:</i> Embrace seasonal trends, prioritize user-friendly navigation.</li></ul><p><strong>Electronics:</strong></p><ul><li><i>Description:</i> Cutting-edge gadgets and technology solutions.</li><li><i>Key Points:</i> Ensure detailed product specifications, highlight tech features.</li></ul><p><strong>Home and Furniture:</strong></p><ul><li><i>Description:</i> Transform living spaces with stylish furniture and decor.</li><li><i>Key Points:</i> Showcase versatile designs, emphasize quality and durability.</li></ul><p><strong>Beauty and Personal Care:</strong></p><ul><li><i>Description:</i> Curated skincare, cosmetics, and self-care essentials.</li><li><i>Key Points:</i> Highlight natural ingredients, provide beauty tips.</li></ul><p><strong>Sports and Outdoors:</strong></p><ul><li><i>Description:</i> Gear for fitness enthusiasts and outdoor adventurers.</li><li><i>Key Points:</i> Prioritize durability, offer expert reviews on equipment.</li></ul><p><strong>Books and Media:</strong></p><ul><li><i>Description:</i> Dive into a world of literature, music, and movies.</li><li><i>Key Points:</i> Implement user reviews, suggest personalized recommendations.</li></ul><p><strong>Toys and Games:</strong></p><ul><li><i>Description:</i> Fun and educational toys for all ages.</li><li><i>Key Points:</i> Categorize by age group, promote learning through play.</li></ul><p><strong>Health and Wellness:</strong></p><ul><li><i>Description:</i> Holistic solutions for physical and mental well-being.</li><li><i>Key Points:</i> Provide expert health advice, offer organic alternatives.</li></ul><p><strong>Automotive and Tools:</strong></p><ul><li><i>Description:</i> Tools, accessories, and automotive essentials.</li><li><i>Key Points:</i> Offer comprehensive product guides, emphasize quality.</li></ul><p><strong>Food and Grocery:</strong></p><ul><li><i>Description:</i> Fresh produce, gourmet delights, and everyday essentials.</li><li><i>Key Points:</i> Ensure reliable delivery, highlight organic and local options.&nbsp;</li></ul></blockquote>`;
const schema = yup
  .object({
    gettingOldUserRate: yup
      .number()
      .typeError("Please enter a valid number")
      .min(0, "Value must be greater than or equal to 0")
      // .max(100, "Value must be less than or equal to 100")
      .required("Please enter old user getting"),
    gettingNewUserRate: yup
      .number()
      .typeError("Please enter a valid number")
      .min(0, "Value must be greater than or equal to 0")
      // .max(100, "Value must be less than or equal to 100")
      .required("Please enter new user getting"),
    sectionLink: yup.string().required("please enter a section link"),
    name: yup.string().required("please enter a name"),
    description: yup.string().required("please enter category description"),
  })
  .required();
function CreateCategory() {
  const [store, setStore] = useState("");
  const [affiliation, setAffiliation] = useState("");
  const [notes, setNotes] = useState(categoryNotes);
  const [device, setDevice] = useState("website");
  const [userGettingType, setUserGettingType] = useState("percent");
  const [gettingType, setGettingType] = useState("percent");
  const [newUserOfferAmount, setNewUserOfferAmount] = useState(null);
  const [oldUserOfferAmount, setOldUserOfferAmount] = useState(null);
  const [newUserOfferPercent, setNewUserOfferPercent] = useState(null);
  const [oldUserOfferPercent, setOldUserOfferPercent] = useState(null);
  const [baseLink, setBaseLink] = useState("");

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);

  useEffect(() => {
    dispatch(fetchAllAffiliationsList());
    dispatch(fetchAllStoresList());
  }, [dispatch]);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const onSubmit = async (data) => {
    if (!affiliation) {
      return toast.custom(<Warning message={"please select affiliation"} />);
    }
    if (!store) {
      return toast.custom(<Warning message={"please select category store"} />);
    }
    if (!notes) {
      return toast.custom(<Warning message={"please select affiliation"} />);
    }
    const payload = {
      ...data,
      affiliation:
        typeof affiliation === "string" ? affiliation : affiliation?.value,

      store,
      device,
      notes,
      gettingType,
      givingType: userGettingType,
    };
    if (userGettingType === "amount") {
      payload.newUserOfferAmount = newUserOfferAmount;
      payload.oldUserOfferAmount = oldUserOfferAmount;
      payload.newUserOfferPercent = null;
      payload.oldUserOfferPercent = null;
    }
    if (userGettingType === "percent") {
      payload.newUserOfferPercent = newUserOfferPercent;
      payload.oldUserOfferPercent = oldUserOfferPercent;
      payload.newUserOfferAmount = null;
      payload.oldUserOfferAmount = null;
    }

    // dispatch(createStoreCategory(payload));
    const resData = await dispatch(createStoreCategory(payload)).unwrap();
    if (resData?.success) {
      navigate("/store-categories/all-store-categories");
    }
  };
  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
  };
  const handleSelectStore = (values) => {
    setStore(values?.value);
    dispatch(fetchStoreDetails(values?.value));
  };

  const handleBaseLinkClick = async () => {
    if (store) {
      const selectedStore = allStoresList.find((item) => item._id === store);
      setValue("sectionLink", selectedStore?.affiliateLink);
    } else {
      toast.custom(<Warning message={"Select a store to set Section Link"} />);
    }
  };

  const { storeDetails } = useSelector((state) => state.store);

  useEffect(() => {
    if (storeDetails?.affiliation) {
      setAffiliation({
        label: storeDetails?.affiliation?.name,
        value: storeDetails?.affiliation?._id,
      });
    }
  }, [storeDetails]);

  const location = useLocation();
  useEffect(() => {
    return () => {
      dispatch(resetStoreDetails());
    };
  }, [location]);

  return (
    <CContainer fluid>
      <h3 className="text-center">Create New Store Category</h3>
      <CRow className="justify-content-center ">
        <CCol md={12} lg={11} xl={9}>
          <CForm onSubmit={handleSubmit(onSubmit)}>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">App Category</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <input
                  type="checkbox"
                  name=""
                  id=""
                  onChange={(e) => {
                    if (e.target.checked) {
                      setDevice("app");
                    } else {
                      setDevice("website");
                    }
                  }}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Category Store (Stores) </p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  array={allStoresList}
                  handleSelectedValue={handleSelectStore}
                  placeholder={"Select Store..."}
                />
              </CCol>
            </CRow>
            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Affiliation</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <SearchAndSelect
                  defaultValue={affiliation}
                  array={allAffiliationsList}
                  handleSelectedValue={handleSelectAffiliations}
                  placeholder={"Select Partners..."}
                />
              </CCol>
            </CRow>
            <TextAreaField
              state={"name"}
              title={"Category Name"}
              type={"text"}
              setState={register}
              error={errors?.name?.message}
            />
            <TextAreaField
              state={"description"}
              title={"Category Description"}
              type={"text"}
              setState={register}
              error={errors?.description?.message}
            />
            <CRow className="my-5">
              <CCol
                md={3}
                lg={3}
                xl={3}
                className="d-flex justify-content-start align-items-center"
              >
                <p className="me-3 my-auto text-capitalize ">
                  Getting Cashback Type
                </p>
              </CCol>
              <CCol md={9} lg={9} xl={9} className=" ">
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"percent"}
                    checked={gettingType === "percent"}
                    onChange={(e) => setGettingType(e.target.value)}
                  />
                  <label htmlFor="">Percent</label>
                </div>
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"amount"}
                    checked={gettingType === "amount"}
                    onChange={(e) => setGettingType(e.target.value)}
                  />
                  <label htmlFor="">Amount</label>
                </div>
              </CCol>
            </CRow>
            <InputField
              state={"gettingNewUserRate"}
              title={"New User - Getting"}
              type="decimal"
              step="0.01"
              setState={register}
              error={errors.gettingNewUserRate?.message}
            />
            <InputField
              state={"gettingOldUserRate"}
              title={"Old User - Getting"}
              type="decimal"
              step="0.01"
              setState={register}
              error={errors.gettingOldUserRate?.message}
            />
            <CRow className="my-5">
              <CCol
                md={3}
                lg={3}
                xl={3}
                className="d-flex justify-content-start align-items-center"
              >
                <p className="me-3 my-auto text-capitalize ">
                  Giving Cashback Type
                </p>
              </CCol>
              <CCol md={9} lg={9} xl={9} className=" ">
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"percent"}
                    checked={userGettingType === "percent"}
                    onChange={(e) => setUserGettingType(e.target.value)}
                  />
                  <label htmlFor="">Percent</label>
                </div>
                <div>
                  <input
                    className={` `}
                    type={"radio"}
                    value={"amount"}
                    checked={userGettingType === "amount"}
                    onChange={(e) => setUserGettingType(e.target.value)}
                  />
                  <label htmlFor="">Amount</label>
                </div>
              </CCol>
            </CRow>
            {userGettingType === "amount" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    Old User Amount
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"Old User Amount"}
                    type="decimal"
                    value={oldUserOfferAmount}
                    onChange={(e) => setOldUserOfferAmount(e.target.value)}
                    step="0.01"
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}

            {userGettingType === "percent" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    Old User Percentage
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"Old User Percentage"}
                    type="decimal"
                    step="0.01"
                    value={oldUserOfferPercent}
                    onChange={(e) => setOldUserOfferPercent(e.target.value)}
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}
            {userGettingType === "amount" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    New User Amount
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"New User Amount"}
                    type="decimal"
                    value={newUserOfferAmount}
                    onChange={(e) => setNewUserOfferAmount(e.target.value)}
                    step="0.01"
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}
            {userGettingType === "percent" && (
              <CRow className="my-5">
                <CCol
                  md={3}
                  lg={3}
                  xl={3}
                  className="d-flex justify-content-start align-items-center"
                >
                  <p className="me-3 my-auto text-capitalize ">
                    New User Percentage
                  </p>
                </CCol>
                <CCol md={9} lg={9} xl={9} className="position-relative ">
                  <input
                    className={`w-100 border  rounded p-2 `}
                    placeholder={"New User Percentage"}
                    type="decimal"
                    step="0.01"
                    value={newUserOfferPercent}
                    onChange={(e) => setNewUserOfferPercent(e.target.value)}
                  />
                  <p
                    style={{ right: 10 }}
                    className=" text-danger  position-absolute"
                  ></p>
                </CCol>
              </CRow>
            )}

            <TextAreaField
              state={"sectionLink"}
              title={"Section Link"}
              type={"text"}
              setState={register}
              error={errors?.sectionLink?.message}
            />
            <CRow>
              <div className="text-end  ">
                <CButton
                  // type="reset"
                  onClick={() => {
                    handleBaseLinkClick();
                  }}
                  className="text-center"
                  style={{
                    marginTop: -30,
                    backgroundColor: "#4BAAC8",
                    borderColor: "#4BAAC8",
                    color: "white",
                  }}
                >
                  Base Link
                </CButton>
              </div>
            </CRow>

            <CRow className="my-3">
              <CCol md={3} lg={3} xl={3}>
                <p className="me-3 my-auto">Notes</p>
              </CCol>
              <CCol md={9} lg={9} xl={9}>
                <MyCkEditor content={notes} setData={setNotes} />
              </CCol>
            </CRow>
            <CRow>
              <div className="text-center mt-4 pt-4">
                <CButton
                  type="reset"
                  onClick={() =>
                    navigate("/store-categories/all-store-categories")
                  }
                  className="text-light ms-auto mx-1"
                  color="danger"
                >
                  Cancel
                </CButton>
                <CButton
                  type="submit"
                  className="text-light me-auto mx-1"
                  color="success"
                >
                  Create
                </CButton>
              </div>
            </CRow>
          </CForm>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default CreateCategory;
