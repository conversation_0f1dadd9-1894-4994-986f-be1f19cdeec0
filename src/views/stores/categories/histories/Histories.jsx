import { cilArrowBottom } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  <PERSON><PERSON>,
  <PERSON>ontainer,
  CFormCheck,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import Danger from "src/components/alerts/Danger/Danger";
import Warning from "src/components/alerts/Warning/Warning";
import "../../store.css";
import ReadMore from "src/components/text/ReadMore";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAllStoreCategoryHistory,
  fetchAllStoresList,
} from "src/redux/features";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import PaginationComponent from "src/components/pagination/Pagination";
import LoadingComponent from "src/components/loader/LoadingComponent";
import { useNavigate } from "react-router-dom";

function Histories() {
  const [allStCategories, setAllStCategories] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [maxPage, setMaxPage] = useState(1);
  const [search, setSearch] = useState("");
  const [name, setName] = useState(false);
  const [highRate, sethighRate] = useState(false);
  const [lowRate, setlowRate] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [store, setStore] = useState("");
  const [app, setApp] = useState(false);
  const [website, setWebsite] = useState(false);
  const [refresh, setRefresh] = useState(false);
  // const [storeParam, setStoreParam] = useState("");

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { allStoresList } = useSelector((state) => state.store);
  const { allCategoriesHistory, page, pages, loading } = useSelector(
    (state) => state.storeCategoryHistory
  );

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const storeParam = searchParams.get("store") || "";
    setStore(storeParam);
    dispatch(fetchAllStoresList());
    dispatch(fetchAllStoreCategoryHistory({ store: storeParam }));
  }, [dispatch]);

  const handlePagination = async (value) => {
    dispatch(fetchAllStoreCategoryHistory({ page: value, store: store }));
  };
  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllStoreCategoryHistory({ search: value, store: store }));
  };

  const handleReset = async (e) => {
    try {
      e.preventDefault();
      setStore("");
      setApp(false);
      setWebsite(false);
      dispatch(fetchAllStoreCategoryHistory({}));
    } catch (error) {
      console.log(error);
    }
  };
  const handleFilter = async (e) => {
    try {
      e.preventDefault();
      const filter = {
        page: currentPage,
        keyword: search,
        name,
        store,
        lowRate,
        highRate,
        app,
        website,
        startDate,
        endDate,
        // store: storeParam,
        platform: app ? "app" : "website",
      };
      dispatch(fetchAllStoreCategoryHistory(filter));
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 403) {
        return toast.custom(<Warning message={"permission denied!"} />);
      }
      toast.custom(
        <Danger message={error?.response?.data?.errors || error.message} />
      );
    }
  };
  const handleSort = async (value) => {
    try {
      setName(false);
      setlowRate(false);
      sethighRate(false);
      if (value === "name") setName(true);
      if (value === "low") setlowRate(true);
      if (value === "high") sethighRate(true);

      const name = value === "name" ? true : false;
      const low = value === "low" ? true : false;
      const high = value === "high" ? true : false;

      const filter = {
        page: 1,
        keyword: search,
        name,
        store,
        lowRate: low,
        highRate: high,
        app,
        website,
        startDate,
        endDate,
        platform: app ? "app" : "website",
      };

      dispatch(fetchAllStoreCategoryHistory(filter));
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 403) {
        return toast.custom(<Warning message={"permission denied!"} />);
      }
      toast.custom(
        <Danger message={error?.response?.data?.errors || error.message} />
      );
    }
  };
  const handleSelectStore = (values) => {
    const searchParams = new URLSearchParams(location.search);

    if (values?.value) {
      setStore(values.value);
      searchParams.set("store", values.value);
    } else {
      setStore("");
      searchParams.delete("store");
    }
    navigate(`${location.pathname}?${searchParams.toString()}`, {
      replace: true,
    });
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="">
        <CCol
          md={6}
          className="my-2 d-flex justify-content-start align-items-center "
        >
          <input
            type="text"
            className="w-50 border rounded py-1 px-2 "
            placeholder="Search...."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </CCol>
      </CRow>
      <CRow className="my-2 border rounded p-2 py-3 ">
        <CCol xs={12} sm={6} md={4} xl={4} className="">
          <SearchAndSelect
            array={allStoresList}
            handleSelectedValue={handleSelectStore}
            placeholder={"Select Store..."}
            defaultValue={
              allStoresList.find((item) => item._id === store)
                ? {
                    value: allStoresList.find((item) => item._id === store)._id,
                    label: allStoresList.find((item) => item._id === store)
                      .name,
                  }
                : null
            }
          />
        </CCol>
        <CCol xs={12} sm={6} md={4} xl={4} className="">
          <div className="m-1">
            <p className="h6">App / Desktop</p>
            <div className="d-flex w-50 justify-content-around">
              <CFormCheck
                type="radio"
                name="exampleRadios"
                id="exampleRadios1"
                value="0"
                onClick={() => {
                  setApp(true), setWebsite(false);
                }}
                label="App"
                checked={app}
              />
              <CFormCheck
                type="radio"
                name="exampleRadios"
                id="exampleRadios2"
                value="0"
                onClick={() => {
                  setApp(false), setWebsite(true);
                }}
                label="Website"
                checked={website}
              />
            </div>
          </div>
        </CCol>
        <CCol xs={12} sm={6} className="d-flex justify-content-between mt-3 ">
          <button
            onClick={handleReset}
            className=" border rounded px-4  fw-bold py-1 shadow border-danger text-danger text-uppercase"
          >
            reset
          </button>
          <button
            onClick={handleFilter}
            className=" border rounded px-4 py-1 fw-bold shadow border-primary text-info text-uppercase"
          >
            filter
          </button>
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol className=" fw-bold py-2" sm={2}>
          Sort By:
        </CCol>
        <CCol className="d-flex justify-content-end  p-2 " sm={10}>
          <button
            type="button"
            className={`${
              name ? "border-bottom border-primary border-3 bg-none" : " "
            }  `}
            onClick={() => handleSort("name")}
          >
            {" "}
            name{" "}
            <CIcon
              className={`${name ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            className={` ${
              lowRate ? "border-bottom border-primary border-3 bg-none " : " "
            } `}
            onClick={() => handleSort("low")}
          >
            Rates Low To High{" "}
            <CIcon
              className={`${lowRate ? "rotate text-danger" : "reverse "} `}
              icon={cilArrowBottom}
            />
          </button>
          <button
            className={` ${
              highRate ? " border-bottom border-primary border-3 bg-none" : " "
            }  `}
            onClick={() => handleSort("high")}
          >
            Rates High to Low{" "}
            <CIcon
              className={`${highRate ? "rotate text-danger" : " reverse"} `}
              icon={cilArrowBottom}
            />
          </button>
        </CCol>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="m-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>NO</CTableHeaderCell>
              <CTableHeaderCell>store</CTableHeaderCell>
              <CTableHeaderCell>category</CTableHeaderCell>
              <CTableHeaderCell>App/Desktop</CTableHeaderCell>
              <CTableHeaderCell>Affiliations</CTableHeaderCell>
              <CTableHeaderCell>Link</CTableHeaderCell>
              <CTableHeaderCell>Getting new User</CTableHeaderCell>
              <CTableHeaderCell>Getting old User</CTableHeaderCell>
              <CTableHeaderCell>Giving new User </CTableHeaderCell>
              <CTableHeaderCell>Giving old User</CTableHeaderCell>
              <CTableHeaderCell>changed By</CTableHeaderCell>
              <CTableHeaderCell>notes</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allCategoriesHistory?.map((item) => (
                <CTableRow
                  v-for="item in tableItems"
                  color={item.active ? " " : "danger"}
                  key={item._id}
                >
                  <CTableDataCell>
                    <div>{item?.uid} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.store?.name} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <ReadMore text={item?.name} />
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.device} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.affiliation.name} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      <ReadMore text={item?.sectionLink} />{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.gettingNewUserRate} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.gettingOldUserRate} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.newUserOfferAmount
                        ? item?.newUserOfferAmount
                        : item?.newUserOfferPercent}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.oldUserOfferAmount
                        ? item?.oldUserOfferAmount
                        : item?.oldUserOfferPercent}{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>
                      {item?.createdBy?.name} /{" "}
                      {new Date(item?.updatedAt).toLocaleString("en-US", {
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })}{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <ReadMore text={item?.notes} />
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default Histories;
