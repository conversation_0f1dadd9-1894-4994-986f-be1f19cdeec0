import { cilTrash } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination, CTableDataCell } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import DeleteAlert from "src/components/alerts/delete/DeleteAlert";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import { fetchAllStoresList } from "src/redux/features";
import { fetchAllStoreReviews } from "src/redux/features/review.store";

function AllReviews() {
  const [store, setStore] = useState("");
  const [search, setSearch] = useState("");
  // delete preview
  const [show, setShow] = useState(false);
  const [reviewId, setReviewId] = useState("");

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allStoreReviews, page, pages, pageSize, loading } = useSelector(
    (state) => state.storeReview
  );
  const { allStoresList } = useSelector((state) => state.store);
  useEffect(() => {
    dispatch(fetchAllStoresList());
  }, []);

  useEffect(() => {
    dispatch(fetchAllStoreReviews({ store }));
  }, [dispatch, store]);

  const handleSearch = async (value) => {
    setSearch(value);
    dispatch(fetchAllStoreReviews({ search: value }));
  };

  const handlePagination = async (value) => {
    dispatch(fetchAllStoreReviews({ page: value }));
  };
  const handleDelete = (id) => {
    setReviewId(id);
    setShow(true);
  };
  const deleteReview = async () => {};

  const handleSelectStore = async (values) => {
    dispatch(fetchAllStoreReviews({ store: values?.value }));
  };

  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow className="mb-2">
        <CCol md={4} className="text-center   me-auto">
          <input
            type="text"
            onChange={(e) => handleSearch(e.target.value)}
            className="w-100 rounded border py-2 px-1"
            placeholder="Search reviews..."
          />
        </CCol>
      </CRow>
      <CRow className="mb-2">
        <CCol md={4}>
          <SearchAndSelect
            array={allStoresList}
            handleSelectedValue={handleSelectStore}
            placeholder={"Select Store..."}
          />
        </CCol>
      </CRow>
      <CRow>
        <CTable
          align="middle"
          className="mb-0 border"
          hover
          striped
          bordered
          borderColor="secondary"
          responsive
        >
          <CTableHead color="dark">
            <CTableRow>
              <CTableHeaderCell>No</CTableHeaderCell>
              <CTableHeaderCell>User</CTableHeaderCell>
              <CTableHeaderCell>Store</CTableHeaderCell>
              <CTableHeaderCell>Reviews</CTableHeaderCell>
              <CTableHeaderCell>Rating</CTableHeaderCell>
              <CTableHeaderCell>Create At</CTableHeaderCell>
              <CTableHeaderCell>time</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {!loading &&
              allStoreReviews?.map((item) => (
                <CTableRow key={item._id} className="">
                  <CTableDataCell>
                    <p>{item?.uid}</p>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <p> {item?.reviewer?.name}</p>
                  </CTableDataCell>
                  <CTableDataCell>
                    <p> {item?.store?.name}</p>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <p>{item.review} </p>
                  </CTableDataCell>
                  <CTableDataCell>
                    <p>{item.rating}</p>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <p>{dateFormatter(item.createdAt)} </p>
                  </CTableDataCell>
                  <CTableDataCell>
                    <p> {timeFormatter(item.createdAt)}</p>{" "}
                  </CTableDataCell>
                  <CTableDataCell className="d-flex">
                    {/* <CTooltip content={"edit review"}>
                    <button
                      onClick={() => navigate(`/`)}
                      className={`text-info border rounded shadow px-2 py-1 mx-1  `}
                    >
                      <CIcon size="sm" className="w-100 " icon={cilPen} />
                    </button>
                  </CTooltip> */}
                    <CTooltip content={"delete review"}>
                      <button
                        onClick={() => handleDelete(item._id)}
                        className={`text-danger border rounded shadow px-2 py-1 mx-1  `}
                      >
                        <CIcon size="sm" className="w-100 " icon={cilTrash} />
                      </button>
                    </CTooltip>
                  </CTableDataCell>
                </CTableRow>
              ))}
          </CTableBody>
        </CTable>
        {loading && <LoadingComponent />}
      </CRow>
      <DeleteAlert
        message={"Are you sure to want to delete this review ?"}
        setState={deleteReview}
        setVisible={setShow}
        visible={show}
      />
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default AllReviews;
