import { cilEyedropper } from "@coreui/icons";
import CIcon from "@coreui/icons-react";
import {
  CCol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CTooltip,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import LoadingComponent from "src/components/loader/LoadingComponent";
import PaginationComponent from "src/components/pagination/Pagination";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import {
  fetchAllTermsAndPrivacyLogs,
  handleOpenModal,
} from "src/redux/features";

function TermsAndPrivacyLogs() {
  const dispatch = useDispatch();
  const { page, pages, allTermsAndPrivacyLogs, loading } = useSelector(
    (state) => state.termsAndPrivacyLog
  );

  useEffect(() => {
    dispatch(fetchAllTermsAndPrivacyLogs({}));
  }, [dispatch]);

  const handlePagination = (value) => {
    dispatch(fetchAllTermsAndPrivacyLogs({ page: value }));
  };
  const handleSearch = (value) => {
    dispatch(fetchAllTermsAndPrivacyLogs({ search: value }));
  };
  const handleViewContent = (content) => {
    console.log(content);
    dispatch(handleOpenModal({ content }));
  };
  return (
    <CContainer fluid>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
      <CRow>
        <div className="">
          <input
            type="text"
            onChange={(e) => handleSearch(e.target.value)}
            className="my-2 p-1 px-2 rounded border"
            placeholder="Search..."
          />
        </div>
      </CRow>
      <CRow>
        <CCol>
          <CTable
            align="middle"
            className="mb-0 border"
            hover
            striped
            bordered
            borderColor="secondary"
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell className="text-center">No</CTableHeaderCell>
                <CTableHeaderCell>type</CTableHeaderCell>
                <CTableHeaderCell>Status</CTableHeaderCell>
                <CTableHeaderCell>createdAt</CTableHeaderCell>
                <CTableHeaderCell>Admin</CTableHeaderCell>
                <CTableHeaderCell>Actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {!loading &&
                allTermsAndPrivacyLogs?.map((item) => (
                  <CTableRow
                    v-for="item in tableItems"
                    className=" "
                    color={!item.active ? "danger" : ""}
                    key={item?.uid}
                  >
                    <CTableDataCell className="text-center">
                      <p>{item?.uid}</p>
                    </CTableDataCell>
                    <CTableDataCell className="position-relative ">
                      <div>{item.type}</div>
                    </CTableDataCell>
                    <CTableDataCell className="position-relative ">
                      <div
                        className={` ${
                          item.active ? "text-success " : " text-danger"
                        }`}
                      >
                        {item.active ? "Active " : "InActive "}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div className="small ">
                        {dateFormatter(item.createdAt)}/
                        {timeFormatter(item.createdAt)}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>{item?.createdBy?.name} </div>
                    </CTableDataCell>
                    <CTableDataCell className="">
                      <CTooltip content={"view content"}>
                        <button
                          type="button"
                          className={
                            " border text-success rounded shadow px-2 py-1 mx-1"
                          }
                          onClick={() => handleViewContent(item)}
                        >
                          <CIcon icon={cilEyedropper} />
                        </button>
                      </CTooltip>
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          {loading && <LoadingComponent />}
        </CCol>
      </CRow>
      <PaginationComponent
        page={page}
        pages={pages}
        handlePagination={handlePagination}
      />
    </CContainer>
  );
}

export default TermsAndPrivacyLogs;
