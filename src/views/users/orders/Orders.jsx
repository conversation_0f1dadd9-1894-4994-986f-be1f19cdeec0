import {
  <PERSON>utton,
  <PERSON>ard,
  <PERSON>ard<PERSON>ody,
  <PERSON>ol,
  CContainer,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";
import { CSmartPagination } from "@coreui/react-pro";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import SearchAndSelect from "src/components/select/SearchAndSelect";
import {
  dateFormatter,
  timeFormatter,
} from "src/helperFunctions/dateFormatter";
import {
  fetchAllAffiliationsList,
  fetchAllClicks,
  fetchAllStoresList,
} from "src/redux/features";

function Orders() {
  const [pageData, setPageData] = useState();
  const [search, setSearch] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  // filter
  const [status, setStatus] = useState("");
  const [store, setStore] = useState("");
  const [affiliation, setAffiliation] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { allAffiliationsList } = useSelector((state) => state.affiliation);
  const { allStoresList } = useSelector((state) => state.store);
  useEffect(() => {
    dispatch(fetchAllAffiliationsList({}));
    dispatch(fetchAllStoresList());
    dispatch(fetchAllClicks({}));
  }, [dispatch]);

  const { page, pages, pageSize, allClicks } = useSelector(
    (state) => state.clicks
  );

  const handlePagination = async (value) => {
    dispatch(fetchAllClicks({ page: value }));
  };
  const handleSort = async (e) => {};
  const handleSearch = async (value) => {};
  const handleSelectAffiliations = (values) => {
    setAffiliation(values?.value);
  };
  const handleSelectStores = (values) => {
    setStore(values?.value);
  };
  const handleSelectStatus = (values) => {
    setStatus(values?.value);
  };

  return (
    <CContainer fluid>
      <CRow>
        <CCol md={12}>
          <CCard className="mb-4 border border-warning">
            <div className="d-flex justify-content-between m-3">
              <input
                type="text"
                className="w-sm-100 w-md-25  border rounded  px-2  me-2"
                placeholder="Search...."
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
              />
              <div className="d-none d-sm-block">
                <CSmartPagination
                  activePage={page}
                  className=""
                  pages={pages}
                  onActivePageChange={handlePagination}
                />
              </div>
            </div>
            <CCardBody>
              <CRow>
                <CCol xs={12} md={8} xl={8} className="mb-3">
                  <p className="h6">Registration Date :</p>
                  <div className="d-md-flex">
                    <div>
                      <label htmlFor="">Start Date</label>
                      <input
                        type="date"
                        name=""
                        className="border rounded  p-2 m-2"
                        onChange={(e) => setFromDate(e.target.value)}
                        placeholder=""
                        id=""
                      />
                    </div>
                    <div>
                      <label htmlFor="">End Date</label>
                      <input
                        type="date"
                        name=""
                        className="border rounded  p-2 m-2"
                        onChange={(e) => setToDate(e.target.value)}
                        id=""
                      />
                    </div>
                  </div>
                </CCol>
                <CRow>
                  <CCol md={4}>
                    <SearchAndSelect
                      array={[{ label: "something", value: "" }]}
                      handleSelectedValue={handleSelectStatus}
                      placeholder={"Select Status..."}
                    />
                  </CCol>
                  <CCol md={4}>
                    <SearchAndSelect
                      array={allAffiliationsList}
                      handleSelectedValue={handleSelectAffiliations}
                      placeholder={"Select Partners..."}
                    />
                  </CCol>
                  <CCol md={4}>
                    <SearchAndSelect
                      array={allStoresList}
                      handleSelectedValue={handleSelectStores}
                      placeholder={"Select Stores..."}
                    />
                  </CCol>
                </CRow>
                <CCol xs={12} className="text-end">
                  <button className="border rounded border-danger shadow px-4 py-1 text-danger fw-bold  m-2">
                    CANCEL
                  </button>
                  <button
                    className="border rounded border-primary shadow px-4 py-1 text-primary fw-bold  m-2"
                    onClick={handleSort}
                  >
                    FILTER
                  </button>
                </CCol>
              </CRow>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <CRow>
        <CCol className="bg-white border py-3 rounded" md={12}>
          <CTable
            align="middle"
            className=" border "
            striped
            hover
            bordered
            responsive
          >
            <CTableHead color="dark">
              <CTableRow>
                <CTableHeaderCell>NO</CTableHeaderCell>
                <CTableHeaderCell>userId</CTableHeaderCell>
                <CTableHeaderCell className="text-center">
                  ClickId
                </CTableHeaderCell>
                <CTableHeaderCell>User</CTableHeaderCell>
                <CTableHeaderCell className="text-center">
                  User Email
                </CTableHeaderCell>
                <CTableHeaderCell>Store </CTableHeaderCell>
                <CTableHeaderCell>Offer Id</CTableHeaderCell>
                <CTableHeaderCell>Offer Title</CTableHeaderCell>
                <CTableHeaderCell>Offer Affiliation</CTableHeaderCell>
                <CTableHeaderCell>User IP</CTableHeaderCell>
                <CTableHeaderCell>City</CTableHeaderCell>
                <CTableHeaderCell>Device</CTableHeaderCell>
                <CTableHeaderCell> Date And Time</CTableHeaderCell>
                <CTableHeaderCell>User Notes</CTableHeaderCell>
                <CTableHeaderCell>actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {allClicks?.map((item) => (
                <CTableRow v-for="item in tableItems" key={item.uid}>
                  <CTableDataCell className="text-center">
                    <div className="">{item?.uid}</div>{" "}
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.user?.uid}</div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.clickUniqueId}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.user?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div className="">{item?.user?.email} </div>{" "}
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.store?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.offer?.uid}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.offer?.title}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.affiliation?.name}</div>
                  </CTableDataCell>
                  <CTableDataCell className="text-center">
                    <div>{item?.userIp} </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.userCity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.device}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {" "}
                    <div>
                      {dateFormatter(item?.createdAt)}{" "}
                      <small>{timeFormatter(item?.createdAt)}</small>{" "}
                    </div>
                  </CTableDataCell>
                  <CTableDataCell>
                    <div>{item?.activity}</div>
                  </CTableDataCell>
                  <CTableDataCell>
                    {item?.isApproved ? (
                      <CButton
                        className="text-white "
                        color="success"
                        size="sm"
                      >
                        Approved
                      </CButton>
                    ) : (
                      <CButton
                        className=""
                        size="sm"
                        onClick={() =>
                          navigate(`/users/earnings/create/${item._id}`)
                        }
                      >
                        Approve
                      </CButton>
                    )}
                  </CTableDataCell>
                </CTableRow>
              ))}
            </CTableBody>
          </CTable>
        </CCol>
      </CRow>
    </CContainer>
  );
}

export default Orders;
